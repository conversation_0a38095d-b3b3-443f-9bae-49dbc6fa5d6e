const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/exceljs.min-DHyROiRH.js","assets/main-S9FuxvcD.js","assets/main-CacQHwGx.css","assets/FileSaver.min-DoOa942s.js"])))=>i.map(i=>d[i]);
import{t as y,r as T,u as m,_ as f,w as S}from"./main-S9FuxvcD.js";class D{constructor(){this.storageKey="app_timeline_events",this.rotationsKey="app_rotations",this.rotationColors=["blue","green","purple","orange","red","indigo","pink","teal"]}getTranslation(e,o="ar"){var t;const s=o||localStorage.getItem("language")||"ar";return((t=y[s])==null?void 0:t[e])||y.ar[e]||e}createEventTitle(e,o,s=null){const t=s||localStorage.getItem("language")||"ar";return`${this.getTranslation(`timeline.eventPrefix.${e}`,t)} ${o}`}createEventDescription(e,o=null){const s=o||localStorage.getItem("language")||"ar";return this.getTranslation(`timeline.eventDesc.${e}`,s)}updateExistingEventTitles(){try{const e=this.getTimelineEvents();let o=!1;const s=e.map(t=>{let r=t.title,n=t.description;if(t.title.includes("بداية:")){const i=t.title.replace("بداية:","").trim();r=this.createEventTitle("start",i),o=!0}else if(t.title.includes("نهاية:")){const i=t.title.replace("نهاية:","").trim();r=this.createEventTitle("end",i),o=!0}else if(t.title.includes("تسليم:")||t.title.includes("موعد تسليم:")||t.title.includes("موعد التسليم:")){const i=t.title.replace(/تسليم:|موعد تسليم:|موعد التسليم:/,"").trim();r=this.createEventTitle("due",i),o=!0}else if(t.title.includes("📝 بداية:")){const i=t.title.replace("📝 بداية:","").trim();r=`📝 ${this.createEventTitle("start",i)}`,o=!0}else if(t.title.includes("⏰ موعد تسليم:")){const i=t.title.replace("⏰ موعد تسليم:","").trim();r=`⏰ ${this.createEventTitle("due",i)}`,o=!0}return t.description==="بداية مهمة تدريبية"?(n=this.createEventDescription("taskStart"),o=!0):t.description==="نهاية مهمة تدريبية"||t.description==="موعد تسليم مهمة تدريبية"?(n=this.createEventDescription("taskDue"),o=!0):t.description==="بداية ورشة عمل تدريبية"?(n=this.createEventDescription("workshopStart"),o=!0):t.description==="نهاية ورشة عمل تدريبية"?(n=this.createEventDescription("workshopEnd"),o=!0):t.description==="بداية دورة تدريبية"?(n=this.createEventDescription("courseStart"),o=!0):t.description==="نهاية دورة تدريبية"?(n=this.createEventDescription("courseEnd"),o=!0):t.description==="بداية روتيشن تدريبي"||t.description==="بداية الروتيشن التدريبي"?(n=this.createEventDescription("rotationStart"),o=!0):t.description==="نهاية الروتيشن التدريبي"&&(n=this.createEventDescription("rotationEnd"),o=!0),{...t,title:r,description:n}});return o?(this.saveTimelineEvents(s),console.log("✅ تم تحديث عناوين الأحداث لتستخدم الترجمات"),!0):!1}catch(e){return console.error("❌ خطأ في تحديث عناوين الأحداث:",e),!1}}forceUpdateEventTitles(){console.log("🔄 تشغيل تحديث فوري لعناوين الأحداث...");const e=this.updateExistingEventTitles();return e&&(window.dispatchEvent(new Event("timelineUpdated")),window.dispatchEvent(new Event("timelineTranslationsUpdated"))),e}getEventColorByRotation(e,o){try{const s=new Date(e);for(let t=0;t<o.length;t++){const r=o[t],n=new Date(r.startDate),i=new Date(r.endDate);if(s>=n&&s<=i)return this.rotationColors[t%this.rotationColors.length]}return"gray"}catch(s){return console.error("خطأ في تحديد لون الحدث:",s),"gray"}}async importTimelineFromExcel(e){return new Promise((o,s)=>{console.log("🔄 بدء استيراد ملف Excel:",e.name);const t=new FileReader;t.onload=r=>{try{console.log("📖 قراءة محتوى الملف...");const n=new Uint8Array(r.target.result),i=T(n,{type:"array"});console.log("📊 أوراق العمل المتاحة:",i.SheetNames);const a=i.SheetNames[0],d=i.Sheets[a],c=m.sheet_to_json(d);console.log("📋 البيانات المستخرجة:",c.length,"صف"),console.log("📋 عينة من البيانات:",c.slice(0,3));const g=this.parseExcelData(c);console.log("✅ تم تحليل",g.length,"حدث"),o(g)}catch(n){console.error("❌ خطأ في استيراد Excel:",n),s(n)}},t.onerror=()=>{console.error("❌ فشل في قراءة الملف"),s(new Error("فشل في قراءة الملف"))},t.readAsArrayBuffer(e)})}parseExcelData(e){console.log("🔍 بدء تحليل بيانات Excel...");const o=[];let s=null,t=1;if(!e||e.length===0)return console.warn("⚠️ لا توجد بيانات للتحليل"),o;const r=Object.keys(e[0]||{});return console.log("📊 الأعمدة المتاحة:",r),e.forEach((n,i)=>{try{if(console.log(`📝 تحليل الصف ${i+1}:`,n),!n||typeof n!="object"){console.log("⚠️ صف فارغ أو غير صالح، تخطي...");return}const a=String(n.Rotation||n.rotation||n.Rotation||n.روتيشن||n.الروتيشن||"").trim(),d=String(n.Workshop||n.workshop||n.Workshop||n["ورشة العمل"]||n["ورش العمل"]||"").trim(),c=String(n["Other Activities"]||n.Activities||n["أنشطة أخرى"]||n.الأنشطة||"").trim(),g=n.Date||n.date||n.Date||n.التاريخ||n.تاريخ||"",h=String(n["Time of (Activities,Rotation,Workshop)"]||n.Time||n.time||n.الوقت||"").trim();if(console.log(`📋 القيم المستخرجة - الروتيشن: "${a}", ورشة: "${d}", نشاط: "${c}", تاريخ: "${g}", وقت: "${h}"`),a&&typeof a=="string"&&a.includes("Start of Rotation")){console.log("🔄 بداية روتيشن جديد:",a);const l=this.extractRotationNumber(a);s={id:`rotation_${t++}`,number:l,name:`Rotation ${l}`,type:"rotation_start",startDate:this.parseDate(g),workshops:[]},o.push({id:s.id,title:s.name,description:`بداية ${s.name}`,type:"rotation_start",date:s.startDate,time:"09:00",category:"rotation",status:"scheduled",rotation:s,icon:"🔄",color:"blue"}),console.log("✅ تم إضافة بداية روتيشن:",s.name)}else if(a&&typeof a=="string"&&a.includes("End of Rotation")){if(console.log("🏁 نهاية روتيشن:",a),s){const l=this.parseDate(g);o.push({id:`${s.id}_end`,title:`انتهاء ${s.name}`,description:`نهاية ${s.name}`,type:"rotation_end",date:l,time:"17:00",category:"rotation",status:"scheduled",rotation:s,icon:"✅",color:"green"}),console.log("✅ تم إضافة نهاية روتيشن:",s.name)}}else if(d&&typeof d=="string"&&d.length>0){console.log("🎯 ورشة عمل:",d);const l={id:`workshop_${Date.now()}_${i}`,title:d,description:d,type:"workshop",date:this.parseDate(g),time:this.parseTime(h),duration:this.calculateDuration(h),category:"workshop",status:"scheduled",rotationId:s?s.id:null,rotationName:s?s.name:null,icon:"🎯",color:"purple"};s&&s.workshops.push({id:l.id,title:l.title,date:l.date,time:l.time}),o.push(l),console.log("✅ تم إضافة ورشة عمل:",d)}else c&&typeof c=="string"&&c.length>0?(console.log("📋 نشاط آخر:",c),o.push({id:`activity_${Date.now()}_${i}`,title:c,description:c,type:"activity",date:this.parseDate(g),time:this.parseTime(h),duration:this.calculateDuration(h),category:"activity",status:"scheduled",rotationId:s?s.id:null,rotationName:s?s.name:null,icon:"📋",color:"orange"}),console.log("✅ تم إضافة نشاط:",c)):console.log("⚠️ صف فارغ أو غير مفهوم:",n)}catch(a){console.error(`❌ خطأ في تحليل الصف ${i+1}:`,a,n)}}),console.log("🎉 انتهى التحليل. تم إنشاء",o.length,"حدث"),o.sort((n,i)=>new Date(n.date)-new Date(i.date))}extractRotationNumber(e){if(!e||typeof e!="string")return console.log("⚠️ نص روتيشن غير صالح:",e),1;const o=[/Rotation (\d+)/i,/روتيشن (\d+)/,/الروتيشن (\d+)/,/(\d+)/];for(const s of o){const t=e.match(s);if(t&&t[1]){const r=parseInt(t[1]);return console.log("✅ رقم الروتيشن المستخرج:",r),r}}return console.log("⚠️ لم يتم العثور على رقم روتيشن، استخدام 1"),1}parseDate(e){if(console.log("📅 تحليل التاريخ:",e),!e)return console.log("⚠️ تاريخ فارغ، استخدام التاريخ الحالي"),new Date().toISOString().split("T")[0];if(typeof e=="number"){console.log("🔢 تاريخ Excel رقمي:",e);const s=new Date(1900,0,1),r=new Date(s.getTime()+(e-2)*24*60*60*1e3).toISOString().split("T")[0];return console.log("✅ تاريخ محول:",r),r}const o=[/(\d{1,2})-(\d{1,2})\s+(\w{3})/,/(\d{1,2})\s+(\w{3})/,/(\d{1,2})\/(\d{1,2})\/(\d{4})/,/(\d{4})-(\d{1,2})-(\d{1,2})/];for(const s of o){const t=e.toString().match(s);if(t){if(console.log("📝 تطابق مع النمط:",s,t),t.length===4&&t[3]){const r=parseInt(t[1]),n=t[3],i=new Date().getFullYear(),a=this.getMonthNumber(n),d=new Date(i,a,r).toISOString().split("T")[0];return console.log("✅ تاريخ محول:",d),d}else if(t.length===4){const r=parseInt(t[1]),n=parseInt(t[2])-1,i=parseInt(t[3]),a=new Date(i,n,r).toISOString().split("T")[0];return console.log("✅ تاريخ محول:",a),a}else if(t.length===4){const r=parseInt(t[1]),n=parseInt(t[2])-1,i=parseInt(t[3]),a=new Date(r,n,i).toISOString().split("T")[0];return console.log("✅ تاريخ محول:",a),a}}}try{const s=new Date(e);if(!isNaN(s.getTime())){const t=s.toISOString().split("T")[0];return console.log("✅ تاريخ محول مباشرة:",t),t}}catch(s){console.warn("⚠️ فشل في تحليل التاريخ:",e,s)}return console.log("⚠️ استخدام التاريخ الحالي كبديل"),new Date().toISOString().split("T")[0]}parseTime(e){if(console.log("🕐 تحليل الوقت:",e),!e)return console.log("⚠️ وقت فارغ، استخدام 09:00"),"09:00";const o=[/(\d{1,2}):(\d{2})\s*(AM|PM)/i,/(\d{1,2}):(\d{2})/,/(\d{1,2})\s*(AM|PM)/i,/(\d{1,2}):(\d{2})\s*-\s*(\d{1,2}):(\d{2})\s*(AM|PM)/i];for(const s of o){const t=e.toString().match(s);if(t){console.log("🕐 تطابق مع نمط الوقت:",s,t);let r=parseInt(t[1]);const n=t[2]?t[2]:"00",i=t[3]||t[5];i&&i.toUpperCase()==="PM"&&r!==12?r+=12:i&&i.toUpperCase()==="AM"&&r===12&&(r=0);const a=`${r.toString().padStart(2,"0")}:${n}`;return console.log("✅ وقت محول:",a),a}}return console.log("⚠️ استخدام الوقت الافتراضي 09:00"),"09:00"}calculateDuration(e){if(!e)return 480;const o=e.match(/(\d{1,2}):(\d{2})\s*AM\s*-\s*(\d{1,2}):(\d{2})\s*PM/);if(o){const s=parseInt(o[1]),t=parseInt(o[2]);let r=parseInt(o[3]);const n=parseInt(o[4]);r!==12&&(r+=12);const i=s*60+t;return r*60+n-i}return 480}getMonthNumber(e){if(!e||typeof e!="string")return console.log("⚠️ اسم شهر غير صالح:",e),0;const o={Jan:0,Feb:1,Mar:2,Apr:3,May:4,Jun:5,Jul:6,Aug:7,Sep:8,Oct:9,Nov:10,Dec:11,January:0,February:1,March:2,April:3,May:4,June:5,July:6,August:7,September:8,October:9,November:10,December:11,يناير:0,فبراير:1,مارس:2,أبريل:3,مايو:4,يونيو:5,يوليو:6,أغسطس:7,سبتمبر:8,أكتوبر:9,نوفمبر:10,ديسمبر:11},s=Object.keys(o).find(r=>r.toLowerCase()===e.toLowerCase()),t=s?o[s]:0;return console.log("📅 تحويل الشهر:",e,"→",t),t}saveTimelineEvents(e){try{const o=e.map(t=>{const r={...t};return r.rotation&&typeof r.rotation=="object"&&(r.rotationId=r.rotation.id,r.rotationName=r.rotation.name,delete r.rotation),r.assignedUsers&&Array.isArray(r.assignedUsers)&&(r.assignedCount=r.assignedUsers.length,delete r.assignedUsers),r.participants&&Array.isArray(r.participants)&&(r.participantCount=r.participants.length,delete r.participants),r});localStorage.setItem(this.storageKey,JSON.stringify(o)),console.log("✅ تم حفظ",o.length,"حدث في التايم لاين");const s=e.filter(t=>t.type==="rotation_start").map(t=>({id:t.rotationId||t.id,name:t.rotationName||t.title,startDate:t.date,workshops:[]}));localStorage.setItem(this.rotationsKey,JSON.stringify(s))}catch(o){console.error("خطأ في حفظ أحداث التايم لاين:",o)}}clearAllData(){try{return localStorage.removeItem(this.storageKey),localStorage.removeItem(this.rotationsKey),localStorage.removeItem("timeline_events"),localStorage.removeItem("timeline_rotations"),localStorage.removeItem("timeline_data"),localStorage.removeItem("sample_timeline_events"),localStorage.setItem(this.storageKey,JSON.stringify([])),localStorage.setItem(this.rotationsKey,JSON.stringify([])),console.log("✅ تم حذف جميع البيانات الوهمية وإعادة تعيين التايم لاين"),!0}catch(e){return console.error("❌ خطأ في حذف البيانات:",e),!1}}getTimelineEvents(){try{const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):[]}catch(e){return console.error("خطأ في تحميل أحداث التايم لاين:",e),[]}}getRotations(){const e=localStorage.getItem(this.rotationsKey);return e?JSON.parse(e):[]}syncWorkshopsToTimeline(){try{const e=JSON.parse(localStorage.getItem("app_workshops")||"[]"),o=this.getTimelineEvents();return e.forEach(s=>{if(!o.find(r=>r.type==="workshop"&&r.sourceId===s.id)){const r={id:`workshop_${s.id}`,sourceId:s.id,title:s.title,description:s.description||s.title,type:"workshop",date:s.date,time:s.time||"09:00",duration:s.duration||480,category:"workshop",status:s.status||"scheduled",icon:"🎯",color:"purple",location:s.location,instructor:s.instructor,createdAt:new Date().toISOString()};o.push(r),console.log("✅ تم إضافة ورشة عمل للتايم لاين:",s.title)}}),this.saveTimelineEvents(o),!0}catch(e){return console.error("❌ خطأ في مزامنة ورش العمل:",e),!1}}syncTasksToTimeline(){try{const e=JSON.parse(localStorage.getItem("app_tasks")||"[]"),o=this.getTimelineEvents();return e.forEach(s=>{if(!o.find(r=>r.type==="task"&&r.sourceId===s.id)){const r={id:`task_${s.id}`,sourceId:s.id,title:s.title,description:s.description||s.title,type:"task",date:s.dueDate||s.date,time:s.time||"09:00",duration:s.estimatedTime||60,category:"task",status:s.status||"pending",priority:s.priority||"medium",icon:"📋",color:s.priority==="high"?"red":s.priority==="medium"?"orange":"blue",assignedTo:s.assignedTo,createdAt:new Date().toISOString()};o.push(r),console.log("✅ تم إضافة مهمة للتايم لاين:",s.title)}}),this.saveTimelineEvents(o),!0}catch(e){return console.error("❌ خطأ في مزامنة المهام:",e),!1}}addWorkshopToTimeline(e){try{const o=this.getTimelineEvents(),s={id:`workshop_${e.id}`,sourceId:e.id,title:e.title,description:e.description||e.title,type:"workshop",date:e.date,time:e.time||"09:00",duration:e.duration||480,category:"workshop",status:e.status||"scheduled",icon:"🎯",color:"purple",location:e.location,instructor:e.instructor,createdAt:new Date().toISOString()};return o.push(s),this.saveTimelineEvents(o),console.log("✅ تم إضافة ورشة عمل جديدة للتايم لاين:",e.title),!0}catch(o){return console.error("❌ خطأ في إضافة ورشة العمل للتايم لاين:",o),!1}}addTaskToTimeline(e){try{const o=this.getTimelineEvents(),s={id:`task_${e.id}`,sourceId:e.id,title:e.title,description:e.description||e.title,type:"task",date:e.dueDate||e.date,time:e.time||"09:00",duration:e.estimatedTime||60,category:"task",status:e.status||"pending",priority:e.priority||"medium",icon:"📋",color:e.priority==="high"?"red":e.priority==="medium"?"orange":"blue",assignedTo:e.assignedTo,createdAt:new Date().toISOString()};return o.push(s),this.saveTimelineEvents(o),console.log("✅ تم إضافة مهمة جديدة للتايم لاين:",e.title),!0}catch(o){return console.error("❌ خطأ في إضافة المهمة للتايم لاين:",o),!1}}getStatusDisplayName(e){return{pending:"في الانتظار",active:"نشط",completed:"مكتمل",cancelled:"ملغي",in_progress:"قيد التنفيذ",scheduled:"مجدول",published:"منشور",draft:"مسودة",submitted:"تم التسليم",graded:"تم التقييم",overdue:"متأخر",urgent:"عاجل"}[e]||e||"غير محدد"}addEvent(e){const o=this.getTimelineEvents(),s=o.find(r=>r.sourceId===e.sourceId&&r.type===e.type&&r.date===e.date);if(s)return console.log("⚠️ الحدث موجود بالفعل في التايم لاين:",e.title),s;const t={...e,id:e.id||`event_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return o.push(t),o.sort((r,n)=>new Date(r.date)-new Date(n.date)),this.saveTimelineEvents(o),console.log("✅ تم إضافة حدث جديد للتايم لاين:",t.title),t}updateEvent(e,o){const s=this.getTimelineEvents(),t=s.findIndex(r=>r.id===e);return t!==-1?(s[t]={...s[t],...o,updatedAt:new Date().toISOString()},s.sort((r,n)=>new Date(r.date)-new Date(n.date)),this.saveTimelineEvents(s),s[t]):null}deleteEvent(e){const s=this.getTimelineEvents().filter(t=>t.id!==e);return this.saveTimelineEvents(s),!0}async exportToExcel(){try{const e=(await f(async()=>{const{default:l}=await import("./exceljs.min-DHyROiRH.js").then(u=>u.e);return{default:l}},__vite__mapDeps([0,1,2]))).default,{saveAs:o}=await f(async()=>{const{saveAs:l}=await import("./FileSaver.min-DoOa942s.js").then(u=>u.F);return{saveAs:l}},__vite__mapDeps([3,1,2])),s=this.getTimelineEvents(),r=(()=>{const l=JSON.parse(localStorage.getItem("currentUser")||"{}"),u=l.id||l.email||"default",p=localStorage.getItem(`primaryColor_${u}`)||"#B89966",E=localStorage.getItem(`secondaryColor_${u}`)||"#94BCCB";return{primary:p.replace("#",""),secondary:E.replace("#",""),accent:"F5F5DC",text:"333333",lightGray:"F8F9FA"}})(),n=new e.Workbook;n.creator="نظام إدارة التدريب",n.lastModifiedBy="نظام إدارة التدريب",n.created=new Date,n.modified=new Date;const i=n.addWorksheet("التايم لاين التدريبي"),a=["التاريخ","الوقت","العنوان","الوصف","النوع","الفئة","الحالة","تاريخ البداية","تاريخ النهاية","الروتيشن"];i.addRow(a).eachCell(l=>{l.font={bold:!0,color:{argb:"FFFFFF"}},l.fill={type:"pattern",pattern:"solid",fgColor:{argb:r.primary}},l.alignment={horizontal:"center",vertical:"middle"},l.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}}),s.forEach(l=>{const u=i.addRow([l.date||"-",l.time||"-",l.title||"-",l.description||"-",this.getTypeDisplayName(l.type),this.getCategoryDisplayName(l.category),this.getStatusDisplayName(l.status),l.startDate||"-",l.endDate||l.dueDate||"-",l.rotation?l.rotation.name:"-"]);u.eachCell(p=>{p.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},p.alignment={horizontal:"center",vertical:"middle"}}),l.status==="completed"?u.eachCell(p=>{p.fill={type:"pattern",pattern:"solid",fgColor:{argb:"E8F5E8"}}}):l.status==="overdue"?u.eachCell(p=>{p.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFE8E8"}}}):l.status==="active"&&u.eachCell(p=>{p.fill={type:"pattern",pattern:"solid",fgColor:{argb:r.lightGray}}})}),i.columns=[{width:15},{width:10},{width:30},{width:40},{width:15},{width:15},{width:15},{width:15},{width:15},{width:20}];const c=await n.xlsx.writeBuffer(),g=new Blob([c],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),h=`timeline_${new Date().toISOString().split("T")[0]}.xlsx`;o(g,h),console.log("✅ تم تصدير التايم لاين إلى Excel بنجاح")}catch(e){console.error("❌ خطأ في تصدير التايم لاين:",e);const s=this.getTimelineEvents().map(i=>({التاريخ:i.date,الوقت:i.time,العنوان:i.title,الوصف:i.description,النوع:this.getTypeDisplayName(i.type),الفئة:this.getCategoryDisplayName(i.category),الحالة:this.getStatusDisplayName(i.status),"تاريخ البداية":i.startDate||"-","تاريخ النهاية":i.endDate||i.dueDate||"-",الروتيشن:i.rotation?i.rotation.name:"-"})),t=m.json_to_sheet(s),r=m.book_new();m.book_append_sheet(r,t,"Timeline");const n=`timeline_${new Date().toISOString().split("T")[0]}.xlsx`;S(r,n)}}getTypeDisplayName(e){return{rotation_start:"بداية روتيشن",rotation_end:"نهاية روتيشن",workshop:"ورشة عمل",activity:"نشاط",task:"مهمة"}[e]||e}getCategoryDisplayName(e){return{rotation:"روتيشن",workshop:"ورشة عمل",activity:"نشاط",task:"مهمة"}[e]||e}getStatusDisplayName(e){return{scheduled:"مجدول",in_progress:"قيد التنفيذ",completed:"مكتمل",cancelled:"ملغي",overdue:"متأخر",urgent:"عاجل",pending:"في الانتظار"}[e]||e}syncSystemDataToTimeline(){console.log("🔄 بدء مزامنة البيانات من الأنظمة إلى التايم لاين...");try{return this.syncWithTasks(),this.syncWithWorkshops(),this.syncWithCourses(),this.syncWithRotations(),console.log("✅ تمت مزامنة البيانات من الأنظمة إلى التايم لاين بنجاح"),!0}catch(e){return console.error("❌ خطأ في مزامنة البيانات من الأنظمة إلى التايم لاين:",e),!1}}syncAllData(){console.log("🔄 بدء المزامنة الشاملة للتايم لاين...");try{return this.syncWithTasks(),this.syncWithWorkshops(),this.syncWithCourses(),this.syncWithRotations(),this.syncTimelineToSystems(),console.log("✅ تمت المزامنة الشاملة بنجاح"),!0}catch(e){return console.error("❌ فشل في المزامنة الشاملة:",e),!1}}syncTimelineToSystems(){console.log("🔄 مزامنة من التايم لاين إلى الأنظمة الأخرى...");const e=this.getTimelineEvents();this.syncTimelineWorkshopsToSystem(e),this.syncTimelineTasksToSystem(e),this.syncTimelineCoursesToSystem(e)}syncTimelineWorkshopsToSystem(e){try{const o=JSON.parse(localStorage.getItem("workshops_data")||"[]"),s=e.filter(t=>t.type==="workshop"&&!t.sourceId);console.log("🎯 مزامنة ورش العمل من التايم لاين:",s.length),s.forEach(t=>{if(!o.find(n=>n.title===t.title&&n.startDate===t.date)){const n={id:`workshop_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,title:t.title,description:t.description||"ورشة عمل مستوردة من التايم لاين",startDate:t.date,startTime:t.time||"09:00",duration:t.duration||480,status:"published",trainer:"مستورد من التايم لاين",selectedTrainees:[],createdAt:new Date().toISOString(),source:"timeline"};o.push(n),console.log("➕ تم إضافة ورشة عمل جديدة:",n.title),t.sourceId=n.id}}),localStorage.setItem("workshops_data",JSON.stringify(o)),this.saveTimelineEvents(e)}catch(o){console.error("❌ خطأ في مزامنة ورش العمل:",o)}}syncTimelineTasksToSystem(e){try{const o=JSON.parse(localStorage.getItem("tasks_data")||"[]"),s=e.filter(t=>t.type==="task"&&!t.sourceId);console.log("📝 مزامنة المهام من التايم لاين:",s.length),s.forEach(t=>{if(!o.find(n=>n.title===t.title&&n.dueDate===t.date)){const n={id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,title:t.title,description:t.description||"مهمة مستوردة من التايم لاين",dueDate:t.date,dueTime:t.time||"23:59",status:"published",course:"مستورد من التايم لاين",trainer:"مستورد من التايم لاين",assignedUsers:[],createdAt:new Date().toISOString(),source:"timeline"};o.push(n),console.log("➕ تم إضافة مهمة جديدة:",n.title),t.sourceId=n.id}}),localStorage.setItem("tasks_data",JSON.stringify(o)),this.saveTimelineEvents(e)}catch(o){console.error("❌ خطأ في مزامنة المهام:",o)}}syncTimelineCoursesToSystem(e){try{const o=JSON.parse(localStorage.getItem("courses_data")||"[]"),s=e.filter(t=>t.type==="course"&&!t.sourceId);console.log("📚 مزامنة الدورات من التايم لاين:",s.length),s.forEach(t=>{if(!o.find(n=>n.title===t.title&&n.startDate===t.date)){const n={id:`course_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,title:t.title,description:t.description||"دورة مستوردة من التايم لاين",startDate:t.date,startTime:t.time||"09:00",duration:t.duration||480,status:"published",instructor:"مستورد من التايم لاين",enrolledStudents:[],createdAt:new Date().toISOString(),source:"timeline"};o.push(n),console.log("➕ تم إضافة دورة جديدة:",n.title),t.sourceId=n.id}}),localStorage.setItem("courses_data",JSON.stringify(o)),this.saveTimelineEvents(e)}catch(o){console.error("❌ خطأ في مزامنة الدورات:",o)}}syncWithTasks(){try{const e=JSON.parse(localStorage.getItem("app_tasks")||"[]"),o=this.getTimelineEvents(),s=JSON.parse(localStorage.getItem("rotations_data")||"[]");console.log("🔄 مزامنة المهام:",e.length,"مهمة"),e.forEach(t=>{try{if(JSON.parse(localStorage.getItem("task_blacklist")||"[]").some(a=>t.title&&t.title.includes(a))){console.log("🚫 تجاهل مهمة في القائمة السوداء:",t.title);return}}catch(i){console.error("خطأ في فحص القائمة السوداء:",i)}const r=o.find(i=>i.type==="task_start"&&i.sourceId===t.id),n=o.find(i=>i.type==="task_due"&&i.sourceId===t.id);if(!r&&t.startDate){const i=this.getEventColorByRotation(t.startDate,s);console.log("➕ إضافة بداية المهمة للتايم لاين:",t.title),this.addEvent({title:this.createEventTitle("start",t.title),description:t.description||this.createEventDescription("taskStart"),type:"task_start",date:t.startDate,time:t.startTime||"09:00",category:"task",status:t.status==="published"?"scheduled":"draft",sourceId:t.id,icon:"🚀",color:i,course:t.course,trainer:t.trainer,assignedCount:t.assignedUsers?t.assignedUsers.length:0})}if(!n&&t.dueDate){const i=this.getEventColorByRotation(t.dueDate,s);console.log("➕ إضافة موعد تسليم المهمة للتايم لاين:",t.title),this.addEvent({title:this.createEventTitle("due",t.title),description:t.description||this.createEventDescription("taskDue"),type:"task_due",date:t.dueDate,time:t.dueTime||"23:59",category:"task",status:t.status==="published"?"scheduled":"draft",sourceId:t.id,icon:"📝",color:i,course:t.course,trainer:t.trainer,assignedCount:t.assignedUsers?t.assignedUsers.length:0})}})}catch(e){console.error("خطأ في مزامنة المهام:",e)}}syncWithWorkshops(){try{const e=JSON.parse(localStorage.getItem("app_workshops")||"[]");let o=[];try{const{default:i}=require("../utils/dataStorage");o=i.loadWorkshops()||[]}catch(i){console.log("⚠️ لا يمكن تحميل من dataStorage:",i.message)}const t=[...e,...o].reduce((i,a)=>(i.find(c=>c.id===a.id)||i.push(a),i),[]),r=this.getTimelineEvents(),n=JSON.parse(localStorage.getItem("rotations_data")||"[]");console.log("🔄 مزامنة ورش العمل:",t.length,"ورشة"),t.forEach(i=>{const a=r.find(c=>c.type==="workshop_start"&&c.sourceId===i.id),d=r.find(c=>c.type==="workshop_end"&&c.sourceId===i.id);if(!a&&i.startDate){const c=this.getEventColorByRotation(i.startDate,n);console.log("➕ إضافة بداية ورشة العمل للتايم لاين:",i.title),this.addEvent({title:this.createEventTitle("start",i.title),description:i.description||this.createEventDescription("workshopStart"),type:"workshop_start",date:i.startDate,time:i.startTime||"09:00",category:"workshop",status:i.status==="published"?"scheduled":"draft",sourceId:i.id,icon:"🚀",color:c,trainer:i.trainer,participantCount:i.selectedTrainees?i.selectedTrainees.length:0})}if(!d&&i.endDate){const c=this.getEventColorByRotation(i.endDate,n);console.log("➕ إضافة نهاية ورشة العمل للتايم لاين:",i.title),this.addEvent({title:this.createEventTitle("end",i.title),description:i.description||this.createEventDescription("workshopEnd"),type:"workshop_end",date:i.endDate,time:i.endTime||"17:00",category:"workshop",status:i.status==="published"?"scheduled":"draft",sourceId:i.id,icon:"🎯",color:c,trainer:i.trainer,participantCount:i.selectedTrainees?i.selectedTrainees.length:0})}})}catch(e){console.error("خطأ في مزامنة ورش العمل:",e)}}syncWithCourses(){try{const e=JSON.parse(localStorage.getItem("app_courses")||"[]"),o=this.getTimelineEvents(),s=JSON.parse(localStorage.getItem("rotations_data")||"[]");console.log("📚 الدورات الموجودة في النظام:",e.length),console.log("📚 تفاصيل الدورات:",e.map(t=>({id:t.id,title:t.courseName||t.title,startDate:t.startDate,endDate:t.endDate}))),console.log("🔄 مزامنة الدورات:",e.length,"دورة"),e.forEach(t=>{const r=o.find(i=>i.type==="course_start"&&i.sourceId===t.id),n=o.find(i=>i.type==="course_end"&&i.sourceId===t.id);if(!r&&t.startDate){const i=this.getEventColorByRotation(t.startDate,s);console.log("➕ إضافة بداية الدورة للتايم لاين:",t.title),this.addEvent({title:this.createEventTitle("start",t.courseName||t.title),description:t.description||this.createEventDescription("courseStart"),type:"course_start",date:t.startDate,time:t.startTime||"09:00",category:"course",status:t.status==="published"?"scheduled":"draft",sourceId:t.id,icon:"🚀",color:i,instructor:t.trainer||t.instructor,enrolledCount:t.selectedTrainees?t.selectedTrainees.length:0})}if(!n&&t.endDate){const i=this.getEventColorByRotation(t.endDate,s);console.log("➕ إضافة نهاية الدورة للتايم لاين:",t.title),this.addEvent({title:this.createEventTitle("end",t.courseName||t.title),description:t.description||this.createEventDescription("courseEnd"),type:"course_end",date:t.endDate,time:t.endTime||"17:00",category:"course",status:t.status==="published"?"scheduled":"draft",sourceId:t.id,icon:"📚",color:i,instructor:t.trainer||t.instructor,enrolledCount:t.selectedTrainees?t.selectedTrainees.length:0})}})}catch(e){console.error("خطأ في مزامنة الدورات:",e)}}syncWithRotations(){const e=JSON.parse(localStorage.getItem("app_rotations")||"[]"),o=this.getTimelineEvents();e.forEach(s=>{o.find(n=>n.type==="rotation_start"&&n.sourceId===s.id)||this.addEvent({title:this.createEventTitle("start",s.name),description:s.description||this.createEventDescription("rotationStart"),type:"rotation_start",date:s.startDate,time:s.startTime||"09:00",category:"rotation",status:"scheduled",sourceId:s.id,icon:"🔄",color:"orange",rotation:s}),!o.find(n=>n.type==="rotation_end"&&n.sourceId===s.id)&&s.endDate&&this.addEvent({title:this.createEventTitle("end",s.name),description:this.createEventDescription("rotationEnd"),type:"rotation_end",date:s.endDate,time:s.endTime||"17:00",category:"rotation",status:"scheduled",sourceId:s.id,icon:"✅",color:"green",rotation:s})})}removeEventBySourceId(e,o){try{const t=this.getTimelineEvents().filter(r=>!(r.sourceId===e&&r.type===o));return this.saveTimelineEvents(t),console.log(`✅ تم حذف الحدث من التايم لاين: ${e}`),!0}catch(s){return console.error("خطأ في حذف الحدث من التايم لاين:",s),!1}}removeTaskEvents(e){try{const s=this.getTimelineEvents().filter(t=>{var i;const r=t.category==="task"||((i=t.type)==null?void 0:i.includes("task")),n=t.sourceId===e||t.sourceId===`${e}_start`||t.sourceId===`${e}_due`||t.sourceId===e.toString();return!(r&&n)});return this.saveTimelineEvents(s),console.log(`✅ تم حذف جميع أحداث المهمة من التايم لاين: ${e}`),!0}catch(o){return console.error("خطأ في حذف أحداث المهمة من التايم لاين:",o),!1}}removeWorkshopEvents(e){try{console.log("🗑️ بدء حذف أحداث ورشة العمل من التايم لاين:",e);const o=this.getTimelineEvents();console.log("📊 إجمالي الأحداث قبل الحذف:",o.length);const s=o.filter(r=>{var a,d;const n=r.category==="workshop"||((a=r.type)==null?void 0:a.includes("workshop")),i=r.sourceId===e||r.sourceId===`${e}_start`||r.sourceId===`${e}_end`||r.sourceId===e.toString()||r.sourceId===parseInt(e)||((d=r.title)==null?void 0:d.includes(e));return n&&i});console.log("🔍 الأحداث المرتبطة بورشة العمل:",{workshopId:e,foundEvents:s.length,eventTitles:s.map(r=>r.title)});const t=o.filter(r=>{var a,d;const n=r.category==="workshop"||((a=r.type)==null?void 0:a.includes("workshop")),i=r.sourceId===e||r.sourceId===`${e}_start`||r.sourceId===`${e}_end`||r.sourceId===e.toString()||r.sourceId===parseInt(e)||((d=r.title)==null?void 0:d.includes(e));return!(n&&i)});return console.log("📊 إجمالي الأحداث بعد الحذف:",t.length),console.log("✅ تم حذف",o.length-t.length,"حدث من التايم لاين"),this.saveTimelineEvents(t),console.log(`✅ تم حذف جميع أحداث ورشة العمل من التايم لاين: ${e}`),!0}catch(o){return console.error("❌ خطأ في حذف أحداث ورشة العمل من التايم لاين:",o),!1}}removeEventsByType(e){try{const s=this.getTimelineEvents().filter(t=>t.type!==e);return this.saveTimelineEvents(s),console.log(`✅ تم حذف جميع أحداث ${e} من التايم لاين`),!0}catch(o){return console.error(`خطأ في حذف أحداث ${e} من التايم لاين:`,o),!1}}clearAllEvents(){try{return localStorage.removeItem(this.storageKey),localStorage.removeItem(this.rotationsKey),localStorage.setItem(this.storageKey,JSON.stringify([])),console.log("✅ تم مسح جميع أحداث التايم لاين"),!0}catch(e){return console.error("خطأ في مسح التايم لاين:",e),!1}}resetTimeline(){console.log("🔄 إعادة تعيين التايم لاين..."),localStorage.removeItem(this.storageKey),localStorage.removeItem(this.rotationsKey),console.log("✅ تم إعادة تعيين التايم لاين")}}const _=new D;export{_ as default,_ as timelineService};
//# sourceMappingURL=timelineService-DsyKnWJK.js.map
