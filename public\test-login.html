<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - النظام المحسن</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-accounts {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .test-accounts h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 16px;
        }
        .account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .account:last-child {
            border-bottom: none;
        }
        .account-info {
            flex: 1;
        }
        .account-role {
            font-weight: 600;
            color: #495057;
        }
        .account-email {
            font-size: 14px;
            color: #6c757d;
        }
        .quick-login {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .quick-login:hover {
            background: #218838;
        }
        .status {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 8px;
            background: #e9ecef;
            color: #495057;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 اختبار نظام تسجيل الدخول المحسن</h1>
            <p>اختبار الربط بين الفرونت اند والباك اند</p>
        </div>

        <div class="status" id="status">
            <div class="loading"></div>
            جاري التحقق من حالة النظام...
        </div>

        <div class="test-accounts">
            <h3>حسابات الاختبار المتاحة:</h3>
            <div class="account">
                <div class="account-info">
                    <div class="account-role">مدير النظام</div>
                    <div class="account-email"><EMAIL></div>
                </div>
                <button class="quick-login" onclick="quickLogin('<EMAIL>', 'admin123')">
                    دخول سريع
                </button>
            </div>
            <div class="account">
                <div class="account-info">
                    <div class="account-role">المدرب</div>
                    <div class="account-email"><EMAIL></div>
                </div>
                <button class="quick-login" onclick="quickLogin('<EMAIL>', 'trainer123')">
                    دخول سريع
                </button>
            </div>
            <div class="account">
                <div class="account-info">
                    <div class="account-role">المتدرب</div>
                    <div class="account-email"><EMAIL></div>
                </div>
                <button class="quick-login" onclick="quickLogin('<EMAIL>', 'trainee123')">
                    دخول سريع
                </button>
            </div>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn" id="loginBtn">
                تسجيل الدخول
            </button>
            <button type="button" class="btn" onclick="testLogout()" style="background: #dc3545;">
                تسجيل الخروج
            </button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        // التحقق من حالة النظام عند التحميل
        window.onload = function() {
            checkSystemStatus();
        };

        // التحقق من حالة النظام
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('status');
            
            try {
                // التحقق من localStorage
                const hasLocalStorage = typeof(Storage) !== "undefined";
                
                // التحقق من الاتصال بالخادم
                const response = await fetch('/');
                
                if (response.ok && hasLocalStorage) {
                    statusDiv.innerHTML = '✅ النظام جاهز ويعمل بشكل طبيعي';
                    statusDiv.style.background = '#d4edda';
                    statusDiv.style.color = '#155724';
                } else {
                    throw new Error('مشكلة في النظام');
                }
            } catch (error) {
                statusDiv.innerHTML = '❌ خطأ في الاتصال بالنظام';
                statusDiv.style.background = '#f8d7da';
                statusDiv.style.color = '#721c24';
            }
        }

        // دخول سريع
        function quickLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }

        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            const loginBtn = document.getElementById('loginBtn');
            
            // تعطيل الزر أثناء المعالجة
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<div class="loading"></div> جاري تسجيل الدخول...';
            
            try {
                // محاكاة استدعاء الباك اند
                const result = await simulateBackendLogin(email, password);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ تم تسجيل الدخول بنجاح!</strong><br>
                        مرحباً ${result.user.name}<br>
                        الدور: ${getRoleDisplayName(result.user.role)}<br>
                        الرمز المميز: ${result.token.substring(0, 20)}...
                    `;
                    
                    // حفظ بيانات الجلسة
                    localStorage.setItem('authToken', result.token);
                    localStorage.setItem('currentUser', JSON.stringify(result.user));
                    
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ فشل تسجيل الدخول</strong><br>${result.error}`;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ خطأ في النظام</strong><br>${error.message}`;
            } finally {
                // إعادة تفعيل الزر
                loginBtn.disabled = false;
                loginBtn.innerHTML = 'تسجيل الدخول';
                resultDiv.style.display = 'block';
            }
        });

        // محاكاة استدعاء الباك اند
        async function simulateBackendLogin(email, password) {
            // محاكاة تأخير الشبكة
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // بيانات المستخدمين (محاكاة قاعدة البيانات)
            const users = {
                '<EMAIL>': {
                    id: 'admin_001',
                    email: '<EMAIL>',
                    name: 'مدير النظام',
                    role: 'admin',
                    password: 'admin123'
                },
                '<EMAIL>': {
                    id: 'trainer_001',
                    email: '<EMAIL>',
                    name: 'المدرب الرئيسي',
                    role: 'trainer',
                    password: 'trainer123'
                },
                '<EMAIL>': {
                    id: 'trainee_001',
                    email: '<EMAIL>',
                    name: 'المتدرب التجريبي',
                    role: 'trainee',
                    password: 'trainee123'
                }
            };

            const user = users[email];
            if (!user) {
                return { success: false, error: 'المستخدم غير موجود' };
            }

            if (user.password !== password) {
                return { success: false, error: 'كلمة المرور غير صحيحة' };
            }

            // إنشاء رمز مميز
            const token = 'enhanced_token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            return {
                success: true,
                user: { ...user, password: undefined },
                token: token
            };
        }

        // تسجيل الخروج
        function testLogout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '<strong>ℹ️ تم تسجيل الخروج بنجاح</strong>';
            resultDiv.style.display = 'block';
        }

        // الحصول على اسم الدور
        function getRoleDisplayName(role) {
            const roleNames = {
                'admin': 'مدير النظام',
                'trainer': 'مدرب',
                'trainee': 'متدرب',
                'course_manager': 'مدير الدورات'
            };
            return roleNames[role] || role;
        }
    </script>
</body>
</html>
