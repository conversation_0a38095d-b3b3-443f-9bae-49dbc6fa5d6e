{"name": "training-management-platform-standalone", "private": true, "version": "1.0.0-standalone", "type": "module", "description": "Smart Training Management Platform - Standalone Version (No API Required)", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "chart.js": "^4.4.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.16", "jspdf": "^3.0.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "socket.io-client": "^4.7.4", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}}