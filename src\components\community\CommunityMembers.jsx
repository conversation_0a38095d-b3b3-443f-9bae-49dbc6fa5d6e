import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Search, 
  Filter, 
  UserCheck, 
  MessageSquare, 
  Mail,
  Phone,
  MapPin,
  Calendar,
  Award,
  Star,
  Crown,
  Shield
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { authService } from '../../config/dataConfig'

const CommunityMembers = () => {
  const { user } = useAuth()
  const { t } = useLanguage()
  const [members, setMembers] = useState([])
  const [filteredMembers, setFilteredMembers] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState('all')
  const [loading, setLoading] = useState(true)
  const [selectedMember, setSelectedMember] = useState(null)
  const [showProfileModal, setShowProfileModal] = useState(false)

  useEffect(() => {
    loadMembers()
  }, [])

  useEffect(() => {
    filterMembers()
  }, [members, searchTerm, selectedRole])

  const loadMembers = async () => {
    try {
      setLoading(true)

      console.log('🔍 تحميل أعضاء المجتمع...')

      // استخدام خدمة المصادقة المحسنة للحصول على جميع المستخدمين
      let users = []

      try {
        // محاولة استخدام authService للحصول على المستخدمين
        const { authService } = await import('../../config/dataConfig')
        if (authService && authService.getAllUsers) {
          const allUsers = await authService.getAllUsers()
          if (Array.isArray(allUsers) && allUsers.length > 0) {
            users = allUsers
            console.log('✅ تم تحميل المستخدمين من authService:', users.length)
          }
        }
      } catch (authError) {
        console.log('⚠️ فشل في تحميل المستخدمين من authService:', authError)
      }

      // إذا لم نحصل على مستخدمين من authService، نحاول localStorage
      if (users.length === 0) {
        console.log('🔄 محاولة تحميل المستخدمين من localStorage...')

        // محاولة تحميل من مصادر متعددة
        const sources = [
          { key: 'app_users', isObject: true },
          { key: 'users_data', isObject: false },
          { key: 'currentUser', isObject: false, single: true }
        ]

        for (const source of sources) {
          try {
            const data = JSON.parse(localStorage.getItem(source.key) || (source.isObject ? '{}' : '[]'))

            if (source.single && data.id) {
              // مستخدم واحد
              users.push(data)
            } else if (source.isObject && Object.keys(data).length > 0) {
              // كائن من المستخدمين
              users = [...users, ...Object.values(data)]
            } else if (Array.isArray(data) && data.length > 0) {
              // مصفوفة من المستخدمين
              users = [...users, ...data]
            }
          } catch (error) {
            console.log(`Could not load from ${source.key}:`, error)
          }
        }
      }

      // إزالة التكرارات
      const uniqueUsers = users.filter((user, index, self) =>
        index === self.findIndex(u =>
          (u.id && user.id && u.id === user.id) ||
          (u.email && user.email && u.email === user.email)
        )
      )

      console.log('👥 تم تحميل المستخدمين:', {
        total: uniqueUsers.length,
        trainees: uniqueUsers.filter(u => u.role === 'trainee').length,
        trainers: uniqueUsers.filter(u => u.role === 'trainer').length,
        admins: uniqueUsers.filter(u => u.role === 'admin').length
      })

      // معالجة وتحسين بيانات المستخدمين
      if (uniqueUsers.length === 0) {
        console.log('📝 لا توجد مستخدمين مسجلين في النظام')
        users = []
      } else {
        console.log('📊 معالجة بيانات المستخدمين:', uniqueUsers.length)

        users = uniqueUsers
          .filter(user => user && (user.id || user.email)) // التأكد من وجود معرف
          .map(user => {
            // تحسين بيانات المستخدم
            const processedUser = {
              ...user,
              // تحسين الصورة الشخصية
              avatar: user.profileImage ||
                     user.avatar ||
                     `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || user.email || 'User')}&background=random&color=fff`,

              // حالة الاتصال (محاكاة - في التطبيق الحقيقي يتم الحصول عليها من الخادم)
              isOnline: user.isOnline || Math.random() > 0.7, // محاكاة 30% متصل

              // آخر ظهور
              lastSeen: user.lastSeen || user.lastLogin || new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),

              // تاريخ الانضمام
              joinedAt: user.createdAt || user.joinedAt || user.joinDate || new Date(),

              // إحصائيات المستخدم
              completedTasks: user.completedTasks || Math.floor(Math.random() * 20),
              points: user.points || Math.floor(Math.random() * 1000),
              level: user.level || Math.floor(Math.random() * 5) + 1,

              // التأكد من وجود الحقول المطلوبة
              name: user.name || user.username || user.email?.split('@')[0] || 'مستخدم',
              email: user.email || '',
              role: user.role || 'trainee',
              department: user.department || 'غير محدد'
            }

            return processedUser
          })
          .sort((a, b) => {
            // ترتيب المستخدمين: المتصلين أولاً، ثم حسب النشاط
            if (a.isOnline && !b.isOnline) return -1
            if (!a.isOnline && b.isOnline) return 1
            return new Date(b.lastSeen) - new Date(a.lastSeen)
          })
      }

      setMembers(users)
      console.log('✅ تم تحميل ومعالجة أعضاء المجتمع بنجاح:', users.length)

      // عرض إحصائيات سريعة
      if (users.length > 0) {
        const onlineCount = users.filter(u => u.isOnline).length
        const roleStats = users.reduce((acc, user) => {
          acc[user.role] = (acc[user.role] || 0) + 1
          return acc
        }, {})

        console.log('📈 إحصائيات المجتمع:', {
          total: users.length,
          online: onlineCount,
          roles: roleStats
        })
      }
    } catch (error) {
      console.error('❌ Error loading members:', error)
      // لا نستخدم بيانات وهمية - نعرض قائمة فارغة
      setMembers([])
    } finally {
      setLoading(false)
    }
  }

  const filterMembers = () => {
    let filtered = members

    // تصفية حسب البحث
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(member => {
        const name = (member.name || '').toLowerCase()
        const email = (member.email || '').toLowerCase()
        const department = (member.department || '').toLowerCase()
        const username = (member.username || '').toLowerCase()

        return name.includes(searchLower) ||
               email.includes(searchLower) ||
               department.includes(searchLower) ||
               username.includes(searchLower)
      })
    }

    // تصفية حسب الدور
    if (selectedRole !== 'all') {
      filtered = filtered.filter(member => member.role === selectedRole)
    }

    // ترتيب النتائج: المتصلين أولاً، ثم حسب النشاط
    filtered.sort((a, b) => {
      if (a.isOnline && !b.isOnline) return -1
      if (!a.isOnline && b.isOnline) return 1
      return new Date(b.lastSeen) - new Date(a.lastSeen)
    })

    setFilteredMembers(filtered)

    console.log('🔍 نتائج الفلترة:', {
      total: members.length,
      filtered: filtered.length,
      searchTerm,
      selectedRole
    })
  }

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin': return Crown
      case 'trainer': return Shield
      case 'course_manager': return Star
      default: return UserCheck
    }
  }

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return 'text-red-600 bg-red-100 dark:bg-red-900/20'
      case 'trainer': return 'text-primary-dynamic bg-primary-100 shadow-primary'
      case 'course_manager': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20'
      default: return 'text-secondary-dynamic bg-secondary-100 shadow-secondary'
    }
  }

  const getRoleName = (role) => {
    switch (role) {
      case 'admin': return t('roles.admin')
      case 'trainer': return t('roles.trainer')
      case 'course_manager': return t('roles.courseManager')
      default: return t('roles.trainee')
    }
  }

  const formatLastSeen = (date) => {
    const now = new Date()
    const diff = now - date
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return t('community.now')
    if (minutes < 60) return t('community.minutesAgo', { count: minutes })
    if (hours < 24) return t('community.hoursAgo', { count: hours })
    return t('community.daysAgo', { count: days })
  }

  const handleViewProfile = (member) => {
    setSelectedMember(member)
    setShowProfileModal(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">{t('community.loadingMembers')}</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder={t('community.searchMembers')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">{t('community.allRoles')}</option>
            <option value="trainee">{t('community.trainees')}</option>
            <option value="trainer">{t('community.trainers')}</option>
            <option value="course_manager">{t('community.courseManagers')}</option>
            <option value="admin">{t('community.systemAdmins')}</option>
          </select>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-primary-dynamic icon-primary" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('community.totalMembers')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{members.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <UserCheck className="w-8 h-8 text-secondary-dynamic icon-secondary" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('community.onlineNow')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {members.filter(m => m.isOnline).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Shield className="w-8 h-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('community.trainers')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {members.filter(m => m.role === 'trainer').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Award className="w-8 h-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('community.trainees')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {members.filter(m => m.role === 'trainee').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Members Grid */}
      {filteredMembers.length === 0 ? (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            {searchTerm || selectedRole !== 'all' ? 'لا توجد نتائج' : 'لا يوجد أعضاء'}
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {searchTerm || selectedRole !== 'all'
              ? 'جرب تغيير معايير البحث أو الفلترة'
              : 'لم يتم تسجيل أي مستخدمين في النظام بعد'
            }
          </p>
          {(searchTerm || selectedRole !== 'all') && (
            <button
              onClick={() => {
                setSearchTerm('')
                setSelectedRole('all')
              }}
              className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              مسح الفلاتر
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredMembers.map((member) => {
            const RoleIcon = getRoleIcon(member.role)
            return (
              <motion.div
                key={member.id || member.email}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ y: -5 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200"
              >
              {/* Avatar and Status */}
              <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                <div className="relative">
                  <img
                    src={member.avatar}
                    alt={member.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  {member.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-secondary-dynamic border-2 border-white dark:border-gray-800 rounded-full"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                    {member.name}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {member.department}
                  </p>
                </div>
              </div>

              {/* Role Badge */}
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mb-3 ${getRoleColor(member.role)}`}>
                <RoleIcon className="w-3 h-3 mr-1" />
                {getRoleName(member.role)}
              </div>

              {/* Stats */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">{t('community.completedTasks')}</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{member.completedTasks}</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">{t('community.points')}</span>
                  <span className="font-medium text-yellow-600">{member.points}</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">{t('community.level')}</span>
                  <span className="font-medium text-primary-dynamic">Level {member.level}</span>
                </div>
              </div>

              {/* Last Seen */}
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                {member.isOnline ? (
                  <span className="text-secondary-dynamic font-medium">{t('community.onlineNow')}</span>
                ) : (
                  <span>{t('community.lastSeen')}: {formatLastSeen(member.lastSeen)}</span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => handleViewProfile(member)}
                  className="flex-1 bg-primary-600 text-white text-xs py-2 px-3 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                >
                  <Mail className="w-3 h-3" />
                  <span>{t('community.viewProfile')}</span>
                </button>
              </div>
            </motion.div>
          )
        })}
        </div>
      )}

      {/* Profile Modal */}
      {showProfileModal && selectedMember && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  الملف الشخصي
                </h3>
                <button
                  onClick={() => setShowProfileModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ✕
                </button>
              </div>

              {/* Profile Content */}
              <div className="space-y-6">
                {/* Avatar and Basic Info */}
                <div className="text-center">
                  <img
                    src={selectedMember.avatar}
                    alt={selectedMember.name}
                    className="w-20 h-20 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h4 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {selectedMember.name}
                  </h4>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2 ${getRoleColor(selectedMember.role)}`}>
                    {getRoleName(selectedMember.role)}
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h5 className="font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                    معلومات الاتصال
                  </h5>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">البريد الإلكتروني:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{selectedMember.email}</span>
                  </div>

                  {selectedMember.phone && (
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">الهاتف:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{selectedMember.phone}</span>
                    </div>
                  )}

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">القسم:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{selectedMember.department}</span>
                  </div>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">تاريخ الانضمام:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {new Date(selectedMember.joinedAt).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                </div>

                {/* Statistics */}
                <div className="space-y-4">
                  <h5 className="font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                    الإحصائيات
                  </h5>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                      <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {selectedMember.completedTasks}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        مهام مكتملة
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                      <div className="text-lg font-semibold text-yellow-600">
                        {selectedMember.points}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {t('community.points')}
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                      <div className="text-lg font-semibold text-blue-600">
                        {selectedMember.level}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {t('community.level')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Status */}
                <div className="space-y-4">
                  <h5 className="font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                    الحالة
                  </h5>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{t('community.currentStatus')}:</span>
                    <span className={`text-sm font-medium ${selectedMember.isOnline ? 'text-green-600' : 'text-gray-500'}`}>
                      {selectedMember.isOnline ? t('community.onlineNow') : `${t('community.lastSeen')}: ${formatLastSeen(selectedMember.lastSeen)}`}
                    </span>
                  </div>
                </div>
              </div>

              {/* Close Button */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setShowProfileModal(false)}
                  className="w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CommunityMembers
