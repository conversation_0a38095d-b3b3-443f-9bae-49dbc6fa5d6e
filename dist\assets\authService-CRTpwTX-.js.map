{"version": 3, "file": "authService-CRTpwTX-.js", "sources": ["../../src/services/authService.js"], "sourcesContent": ["// Enhanced authentication service with user registration\n// Uses localStorage for persistence\n\n// Helper function to get current language\nconst getCurrentLanguage = () => {\n  return localStorage.getItem('language') || 'ar'\n}\n\n// Helper function to get localized error messages\nconst getErrorMessage = (key, fallback) => {\n  const language = getCurrentLanguage()\n  const messages = {\n    en: {\n      userExists: 'User already exists',\n      namePasswordRequired: 'Name and password are required',\n      accountCreationFailed: 'Failed to create account',\n      emailNotFound: 'Email not found',\n      accountNotActive: 'Account is not active',\n      incorrectPassword: 'Incorrect password',\n      loginFailed: 'Login failed',\n      invalidToken: 'Invalid token',\n      userNotFoundOrInactive: 'User not found or inactive',\n      getUserDataFailed: 'Failed to get user data',\n      getUsersFailed: 'Failed to get users',\n      userNotFound: 'User not found',\n      cannotDeleteAdmin: 'Cannot delete admin user',\n      userDeleteFailed: 'Failed to delete user',\n      userUpdateFailed: 'Failed to update user',\n      getUserDetailsFailed: 'Failed to get user details',\n      userDeleted: 'User deleted successfully',\n      userUpdated: 'User updated successfully',\n      dataCleared: 'All data cleared and system reinitialized',\n      dataClearFailed: 'Failed to clear data'\n    },\n    ar: {\n      userExists: 'المستخدم موجود بالفعل',\n      namePasswordRequired: 'الاسم وكلمة المرور مطلوبان',\n      accountCreationFailed: 'فشل في إنشاء الحساب',\n      emailNotFound: 'الاسم أو البريد الإلكتروني غير موجود',\n      accountNotActive: 'الحساب غير مفعل',\n      incorrectPassword: 'كلمة المرور غير صحيحة',\n      loginFailed: 'فشل في تسجيل الدخول',\n      invalidToken: 'رمز غير صالح',\n      userNotFoundOrInactive: 'المستخدم غير موجود أو غير مفعل',\n      getUserDataFailed: 'فشل في جلب بيانات المستخدم',\n      getUsersFailed: 'فشل في جلب المستخدمين',\n      userNotFound: 'المستخدم غير موجود',\n      cannotDeleteAdmin: 'لا يمكن حذف مدير النظام',\n      userDeleteFailed: 'فشل في حذف المستخدم',\n      userUpdateFailed: 'فشل في تحديث المستخدم',\n      getUserDetailsFailed: 'فشل في جلب تفاصيل المستخدم',\n      userDeleted: 'تم حذف المستخدم بنجاح',\n      userUpdated: 'تم تحديث المستخدم بنجاح',\n      dataCleared: 'تم مسح جميع البيانات وإعادة تهيئة النظام',\n      dataClearFailed: 'فشل في مسح البيانات'\n    }\n  }\n  return messages[language]?.[key] || fallback\n}\n\n// دالة تسجيل أحداث الأمان\nconst logSecurityEvent = (eventType, data) => {\n  try {\n    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]')\n    const newLog = {\n      id: Date.now(),\n      type: eventType,\n      timestamp: new Date().toISOString(),\n      data: data,\n      userAgent: navigator.userAgent,\n      ip: 'localhost' // في التطبيق الحقيقي، يجب الحصول على IP الحقيقي\n    }\n\n    logs.push(newLog)\n\n    // الاحتفاظ بآخر 1000 سجل فقط\n    if (logs.length > 1000) {\n      logs.splice(0, logs.length - 1000)\n    }\n\n    localStorage.setItem('security_logs', JSON.stringify(logs))\n    console.log('🔒 تم تسجيل حدث أمان:', eventType, data)\n  } catch (error) {\n    console.error('خطأ في تسجيل حدث الأمان:', error)\n  }\n}\n\n// Initialize users from localStorage - no default users\nconst initializeUsers = () => {\n  const stored = localStorage.getItem('app_users')\n  if (stored) {\n    try {\n      const existingUsers = JSON.parse(stored)\n      console.log('✅ Users loaded from localStorage:', Object.keys(existingUsers))\n      return existingUsers\n    } catch (error) {\n      console.error('❌ Error parsing stored users:', error)\n      localStorage.removeItem('app_users')\n    }\n  }\n\n  console.log('🔄 No users found - starting with empty user base')\n  \n  // Return empty object - no default users\n  const emptyUsers = {}\n  localStorage.setItem('app_users', JSON.stringify(emptyUsers))\n  return emptyUsers\n}\n\nlet mockUsers = initializeUsers()\n\n// دالة للتحقق من سلامة البيانات وإعادة تهيئتها إذا لزم الأمر\nconst ensureUsersIntegrity = () => {\n  try {\n    const stored = localStorage.getItem('app_users')\n    if (!stored) {\n      console.log('🔄 No users found, starting fresh...')\n      mockUsers = initializeUsers()\n      return\n    }\n\n    const parsedUsers = JSON.parse(stored)\n    if (!parsedUsers || typeof parsedUsers !== 'object') {\n      console.log('🔄 Invalid users data, starting fresh...')\n      mockUsers = initializeUsers()\n      return\n    }\n\n    mockUsers = parsedUsers\n    console.log('✅ Users integrity verified:', Object.keys(mockUsers).length)\n  } catch (error) {\n    console.error('❌ Error checking users integrity:', error)\n    // في حالة الخطأ، بدء جديد\n    mockUsers = initializeUsers()\n  }\n}\n\n// تشغيل فحص فوري عند تحميل الملف فقط\nensureUsersIntegrity()\n\n// عرض رسالة ترحيبية مع الدوال المساعدة\nconsole.log(`\n🔧 دوال التشخيص والإصلاح المتاحة:\n=====================================\n• showUsersStatus() - عرض حالة المستخدمين\n• showAllUsersWithPasswords() - عرض جميع المستخدمين مع كلمات المرور\n• fixLoginIssue() - إصلاح مشاكل تسجيل الدخول\n• resetUserPassword('email', 'newPassword') - إعادة تعيين كلمة المرور\n\n• forceResetUsers() - إعادة تعيين جميع البيانات\n\nمثال: resetUserPassword('<EMAIL>', '123456')\n`)\n\n// عرض حالة المستخدمين الحالية\nsetTimeout(() => {\n  console.log('📊 حالة النظام الحالية:')\n  showUsersStatus()\n}, 1000)\n\n// دالة لإعادة تعيين البيانات بالقوة\nconst forceResetUsers = () => {\n  console.log('🔄 إعادة تعيين البيانات بالقوة...')\n  localStorage.removeItem('app_users')\n  localStorage.removeItem('users_data')\n  mockUsers = initializeUsers()\n  console.log('✅ تم إعادة تعيين البيانات:', Object.keys(mockUsers))\n}\n\n// تصدير الدالة للاستخدام الخارجي\nwindow.forceResetUsers = forceResetUsers\n\n// دالة لعرض حالة المستخدمين (للتشخيص)\nconst showUsersStatus = () => {\n  const stored = localStorage.getItem('app_users')\n  const current = Object.keys(mockUsers)\n\n  console.log('📊 حالة المستخدمين:')\n  console.log('  - في localStorage:', stored ? Object.keys(JSON.parse(stored)).length : 0)\n  console.log('  - في mockUsers:', current.length)\n  console.log('  - المستخدمين الحاليين:', current)\n\n  if (stored) {\n    const storedUsers = JSON.parse(stored)\n    console.log('  - المستخدمين المحفوظين:', Object.keys(storedUsers))\n    Object.values(storedUsers).forEach(user => {\n      console.log(`    * ${user.name} (${user.email}) - ${user.role}`)\n    })\n  }\n}\n\nwindow.showUsersStatus = showUsersStatus\n\n// دالة لإصلاح مشكلة تسجيل الدخول\nconst fixLoginIssue = () => {\n  console.log('🔧 إصلاح مشكلة تسجيل الدخول...')\n\n  // التحقق من وجود البيانات\n  const stored = localStorage.getItem('app_users')\n  if (!stored) {\n    console.log('🔧 لا توجد بيانات، إنشاء بيانات جديدة...')\n    mockUsers = initializeUsers()\n    saveUsers()\n    return\n  }\n\n  try {\n    const users = JSON.parse(stored)\n    if (!users || Object.keys(users).length === 0) {\n      console.log('🔧 بيانات فارغة، إنشاء بيانات جديدة...')\n      mockUsers = initializeUsers()\n      saveUsers()\n      return\n    }\n\n    // التحقق من سلامة البيانات\n    let needsRepair = false\n    Object.values(users).forEach(user => {\n      if (!user.password || !user.isActive === undefined) {\n        needsRepair = true\n      }\n    })\n\n    if (needsRepair) {\n      console.log('🔧 إصلاح البيانات التالفة...')\n      Object.values(users).forEach(user => {\n        if (!user.password) user.password = 'password'\n        if (user.isActive === undefined) user.isActive = true\n      })\n      localStorage.setItem('app_users', JSON.stringify(users))\n    }\n\n    mockUsers = users\n    console.log('✅ تم إصلاح المشكلة، المستخدمين المتاحين:', Object.keys(mockUsers).length)\n\n  } catch (error) {\n    console.error('🔧 خطأ في إصلاح البيانات:', error)\n    mockUsers = initializeUsers()\n    saveUsers()\n  }\n}\n\nwindow.fixLoginIssue = fixLoginIssue\n\n// دالة لإعادة تعيين كلمة مرور المستخدم (للطوارئ)\nconst resetUserPassword = (emailOrName, newPassword = 'password') => {\n  try {\n    const users = loadUsers()\n    let user = null\n\n    // البحث عن المستخدم\n    if (emailOrName.includes('@')) {\n      user = users[emailOrName]\n    } else {\n      const foundUser = Object.values(users).find(u =>\n        u.name.toLowerCase() === emailOrName.toLowerCase() ||\n        (u.username && u.username.toLowerCase() === emailOrName.toLowerCase())\n      )\n      if (foundUser) {\n        user = foundUser\n      }\n    }\n\n    if (!user) {\n      console.error('❌ المستخدم غير موجود:', emailOrName)\n      return false\n    }\n\n    // تحديث كلمة المرور\n    user.password = newPassword\n    users[user.email] = user\n    mockUsers = users\n\n    // حفظ التغييرات\n    localStorage.setItem('app_users', JSON.stringify(users))\n\n    console.log(`✅ تم تغيير كلمة مرور ${user.name} إلى: ${newPassword}`)\n    return true\n\n  } catch (error) {\n    console.error('❌ خطأ في إعادة تعيين كلمة المرور:', error)\n    return false\n  }\n}\n\nwindow.resetUserPassword = resetUserPassword\n\n// دالة لعرض جميع المستخدمين مع كلمات المرور (للتشخيص فقط)\nconst showAllUsersWithPasswords = () => {\n  const users = loadUsers()\n  console.log('👥 جميع المستخدمين مع كلمات المرور:')\n  console.log('=====================================')\n\n  if (Object.keys(users).length === 0) {\n    console.log('❌ لا توجد مستخدمين محفوظين')\n    return\n  }\n\n  Object.values(users).forEach((user, index) => {\n    console.log(`${index + 1}. الاسم: ${user.name}`)\n    console.log(`   البريد: ${user.email}`)\n    console.log(`   اسم المستخدم: ${user.username || 'غير محدد'}`)\n    console.log(`   كلمة المرور: ${user.password}`)\n    console.log(`   الدور: ${user.role}`)\n    console.log(`   مفعل: ${user.isActive ? 'نعم' : 'لا'}`)\n    console.log('   ---')\n  })\n\n  console.log(`📊 إجمالي المستخدمين: ${Object.keys(users).length}`)\n}\n\nwindow.showAllUsersWithPasswords = showAllUsersWithPasswords\n\n// تم حذف دالة إنشاء المستخدم التجريبي\n\n// Save users to localStorage with backup\nconst saveUsers = () => {\n  try {\n    // إنشاء نسخة احتياطية قبل الحفظ\n    const currentData = localStorage.getItem('app_users')\n    if (currentData) {\n      localStorage.setItem('app_users_backup', currentData)\n    }\n\n    localStorage.setItem('app_users', JSON.stringify(mockUsers))\n    console.log('✅ Users saved successfully:', Object.keys(mockUsers).length)\n\n    // التحقق من الحفظ\n    const savedData = localStorage.getItem('app_users')\n    if (!savedData) {\n      console.error('❌ Failed to save users to localStorage')\n      // استعادة النسخة الاحتياطية\n      if (currentData) {\n        localStorage.setItem('app_users', currentData)\n      }\n    }\n  } catch (error) {\n    console.error('❌ Error saving users:', error)\n    // محاولة استعادة النسخة الاحتياطية\n    try {\n      const backup = localStorage.getItem('app_users_backup')\n      if (backup) {\n        localStorage.setItem('app_users', backup)\n        console.log('🔄 Restored from backup')\n      }\n    } catch (restoreError) {\n      console.error('❌ Failed to restore backup:', restoreError)\n    }\n  }\n}\n\nconst delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))\n\nexport const authService = {\n  async register(userData) {\n    await delay(1000)\n\n    try {\n      const { email, password, name, role = 'trainee' } = userData\n\n      // Generate unique email if not provided\n      const userEmail = email || `user_${Date.now()}@platform.com`\n\n      // تحميل المستخدمين الحاليين من localStorage\n      const currentUsers = loadUsers()\n\n      // إذا لم توجد بيانات، إنشاء بيانات افتراضية\n      if (Object.keys(currentUsers).length === 0) {\n        mockUsers = initializeUsers()\n      } else {\n        mockUsers = currentUsers\n      }\n\n      // Check if user already exists (only if email is provided)\n      if (email && mockUsers[userEmail]) {\n        throw new Error(getErrorMessage('userExists', 'المستخدم موجود بالفعل'))\n      }\n\n      // Validate required fields (email is now optional)\n      if (!password || !name) {\n        throw new Error(getErrorMessage('namePasswordRequired', 'الاسم وكلمة المرور مطلوبان'))\n      }\n\n      // إنشاء اسم مستخدم فريد إذا لم يتم توفيره\n      const username = userData.username || `user_${Date.now()}`\n\n      // Create new user\n      const newUser = {\n        id: Date.now(),\n        email: userEmail,\n        username: username,\n        name,\n        role,\n        avatar: null,\n        phone: userData.phone || '',\n        department: userData.department || '',\n        gender: userData.gender || '',\n        status: 'active',\n        joinDate: new Date().toISOString(),\n        lastLogin: new Date().toISOString(),\n        createdAt: new Date().toISOString(),\n        isActive: true,\n        password // في التطبيق الحقيقي، يجب تشفير كلمة المرور\n      }\n\n      // Add to users\n      console.log('🔵 إضافة مستخدم جديد:', userEmail)\n      mockUsers[userEmail] = newUser\n      console.log('🔵 عدد المستخدمين بعد الإضافة:', Object.keys(mockUsers).length)\n\n      // حفظ البيانات فوراً\n      saveUsers()\n\n      // التحقق من الحفظ\n      const savedUsers = loadUsers()\n      if (!savedUsers[userEmail]) {\n        console.error('❌ فشل في حفظ المستخدم الجديد')\n        throw new Error('فشل في حفظ بيانات المستخدم')\n      }\n\n      console.log('✅ تم حفظ المستخدم الجديد بنجاح:', userEmail)\n\n      // Generate token\n      const token = `mock-jwt-token-${newUser.id}-${Date.now()}`\n\n      return {\n        token,\n        user: { ...newUser, password: undefined } // لا نرسل كلمة المرور\n      }\n    } catch (error) {\n      console.error('❌ خطأ في إنشاء الحساب:', error.message)\n      throw new Error(error.message || getErrorMessage('accountCreationFailed', 'فشل في إنشاء الحساب'))\n    }\n  },\n\n  async login(emailOrUsername, password) {\n    await delay(1000)\n\n    try {\n      // إصلاح أي مشاكل في البيانات أولاً\n      fixLoginIssue()\n\n      // تحميل المستخدمين من localStorage مباشرة\n      let savedUsers = loadUsers()\n\n      // إذا لم توجد بيانات، إنشاء بيانات افتراضية\n      if (Object.keys(savedUsers).length === 0) {\n        console.log('🔄 لا توجد بيانات محفوظة، إنشاء بيانات افتراضية...')\n        mockUsers = initializeUsers()\n        saveUsers()\n        savedUsers = mockUsers\n      } else {\n        mockUsers = savedUsers\n      }\n\n      console.log('🔍 البحث عن المستخدم:', emailOrUsername)\n      console.log('🔍 المستخدمين المتاحين:', Object.keys(mockUsers))\n      console.log('🔍 عدد المستخدمين:', Object.keys(mockUsers).length)\n\n      // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم\n      let user = null\n\n      // البحث بالبريد الإلكتروني أولاً\n      if (emailOrUsername.includes('@')) {\n        user = mockUsers[emailOrUsername]\n        console.log('🔍 البحث بالبريد الإلكتروني:', user ? 'موجود' : 'غير موجود')\n      } else {\n        // البحث باسم المستخدم أو الاسم (مع تجاهل الحالة)\n        user = Object.values(mockUsers).find(u => {\n          const usernameMatch = u.username && u.username.toLowerCase() === emailOrUsername.toLowerCase()\n          const nameMatch = u.name && u.name.toLowerCase() === emailOrUsername.toLowerCase()\n          const emailMatch = u.email && u.email.toLowerCase() === emailOrUsername.toLowerCase()\n\n          return usernameMatch || nameMatch || emailMatch\n        })\n        console.log('🔍 البحث بالاسم/اسم المستخدم:', user ? 'موجود' : 'غير موجود')\n      }\n\n      if (!user) {\n        console.log('❌ المستخدم غير موجود. تفاصيل البحث:')\n        console.log(`  - البحث عن: \"${emailOrUsername}\"`)\n        console.log(`  - نوع البحث: ${emailOrUsername.includes('@') ? 'بريد إلكتروني' : 'اسم مستخدم/اسم'}`)\n        console.log(`  - عدد المستخدمين المتاحين: ${Object.keys(mockUsers).length}`)\n        console.log('  - المستخدمين المتاحين:')\n        Object.values(mockUsers).forEach(u => {\n          console.log(`    * ${u.name} | ${u.username || 'لا يوجد اسم مستخدم'} | ${u.email}`)\n        })\n\n        // اقتراح مستخدمين مشابهين\n        const suggestions = Object.values(mockUsers).filter(u => {\n          const searchLower = emailOrUsername.toLowerCase()\n          return u.name.toLowerCase().includes(searchLower) ||\n                 u.email.toLowerCase().includes(searchLower) ||\n                 (u.username && u.username.toLowerCase().includes(searchLower))\n        })\n\n        if (suggestions.length > 0) {\n          console.log('  - اقتراحات مشابهة:')\n          suggestions.forEach(s => {\n            console.log(`    * ${s.name} (${s.email})`)\n          })\n        }\n\n        throw new Error(getErrorMessage('emailNotFound', 'الاسم أو البريد الإلكتروني غير موجود'))\n      }\n\n      if (!user.isActive) {\n        throw new Error(getErrorMessage('accountNotActive', 'الحساب غير مفعل'))\n      }\n\n      // تسجيل محاولة تسجيل الدخول\n      logSecurityEvent('login_attempt', {\n        email: emailOrUsername,\n        success: false,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent\n      })\n\n      // Check password (in real app, compare hashed passwords)\n      const userPassword = user.password || 'password' // fallback for old users\n\n      // فحص كلمة المرور مع تسجيل المحاولات الفاشلة\n      if (password !== userPassword) {\n        // تسجيل محاولة فاشلة\n        logSecurityEvent('failed_login_attempt', {\n          email: emailOrUsername,\n          reason: 'incorrect_password',\n          timestamp: new Date().toISOString(),\n          userAgent: navigator.userAgent\n        })\n\n        throw new Error(getErrorMessage('incorrectPassword', 'كلمة المرور غير صحيحة'))\n      }\n\n      // تحديث آخر تسجيل دخول\n      user.lastLogin = new Date().toISOString()\n      mockUsers[user.email] = user\n      saveUsers()\n\n      // Generate token\n      const token = `mock-jwt-token-${user.id}-${Date.now()}`\n\n      // حفظ معلومات الجلسة\n      localStorage.setItem('token', token)\n      localStorage.setItem('current_user', JSON.stringify({ ...user, password: undefined }))\n      localStorage.setItem('login_time', new Date().toISOString())\n\n      // تسجيل نجاح تسجيل الدخول\n      logSecurityEvent('successful_login', {\n        email: emailOrUsername,\n        userId: user.id,\n        userName: user.name,\n        role: user.role,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent\n      })\n\n      console.log('✅ تسجيل دخول ناجح للمستخدم:', user.name)\n\n      return {\n        token,\n        user: { ...user, password: undefined }\n      }\n    } catch (error) {\n      console.error('❌ خطأ في تسجيل الدخول:', error.message)\n      throw new Error(error.message || getErrorMessage('loginFailed', 'فشل في تسجيل الدخول'))\n    }\n  },\n\n  async logout() {\n    // Simulate network delay\n    await delay(500)\n\n    try {\n      // تنظيف بيانات الجلسة فقط - لا نحذف بيانات المستخدمين\n      localStorage.removeItem('token')\n      localStorage.removeItem('current_user')\n      localStorage.removeItem('login_time')\n\n      console.log('✅ تم تسجيل الخروج بنجاح')\n\n      // Mock logout - just return success\n      return { success: true }\n    } catch (error) {\n      console.error('❌ خطأ في تسجيل الخروج:', error)\n      return { success: true } // نعيد نجاح حتى لو حدث خطأ\n    }\n  },\n\n  async getCurrentUser() {\n    await delay(500)\n\n    try {\n      const token = localStorage.getItem('token')\n      if (!token || !token.startsWith('mock-jwt-token')) {\n        throw new Error(getErrorMessage('invalidToken', 'رمز غير صالح'))\n      }\n\n      // التحقق من سلامة البيانات أولاً\n      ensureUsersIntegrity()\n\n      // تحميل المستخدمين من localStorage\n      const savedUsers = loadUsers()\n      if (Object.keys(savedUsers).length === 0) {\n        // إذا لم توجد بيانات محفوظة، إنشاء بيانات افتراضية\n        mockUsers = initializeUsers()\n        saveUsers()\n      } else {\n        mockUsers = savedUsers\n      }\n\n      // محاولة الحصول على المستخدم من البيانات المحفوظة أولاً\n      const savedUser = localStorage.getItem('current_user')\n      if (savedUser) {\n        try {\n          const user = JSON.parse(savedUser)\n          if (user && user.isActive) {\n            return user\n          }\n        } catch (error) {\n          console.error('خطأ في تحليل بيانات المستخدم المحفوظة:', error)\n        }\n      }\n\n      // Extract user ID from token\n      const userId = token.split('-')[3]\n      const user = Object.values(mockUsers).find(u => u.id.toString() === userId)\n\n      if (!user || !user.isActive) {\n        throw new Error(getErrorMessage('userNotFoundOrInactive', 'المستخدم غير موجود أو غير مفعل'))\n      }\n\n      // حفظ بيانات المستخدم للمرة القادمة\n      localStorage.setItem('current_user', JSON.stringify({ ...user, password: undefined }))\n\n      return { ...user, password: undefined }\n    } catch (error) {\n      throw new Error(error.message || getErrorMessage('getUserDataFailed', 'فشل في جلب بيانات المستخدم'))\n    }\n  },\n\n  // Get all users (admin only)\n  async getAllUsers() {\n    await delay(500)\n\n    try {\n      // تحديث mockUsers من localStorage للحصول على أحدث البيانات\n      const savedUsers = loadUsers()\n      console.log('🟢 getAllUsers - loaded from localStorage:', Object.keys(savedUsers))\n\n      // إذا لم توجد بيانات محفوظة، إنشاء بيانات افتراضية\n      if (Object.keys(savedUsers).length === 0) {\n        console.log('🟢 No saved users, initializing defaults')\n        mockUsers = initializeUsers()\n        saveUsers()\n      } else {\n        // استخدام البيانات المحفوظة مباشرة\n        console.log('🟢 Using saved users, before assignment mockUsers has:', Object.keys(mockUsers))\n        mockUsers = savedUsers\n        console.log('🟢 After assignment mockUsers has:', Object.keys(mockUsers))\n      }\n\n      return Object.values(mockUsers).map(user => ({\n        ...user,\n        password: undefined\n      }))\n    } catch (error) {\n      console.error('Error in getAllUsers:', error)\n      throw new Error(getErrorMessage('getUsersFailed', 'فشل في جلب المستخدمين'))\n    }\n  },\n\n  // Update user (admin only)\n  async updateUser(userId, userData) {\n    await delay(500)\n\n    try {\n      // تحميل المستخدمين الحاليين\n      const currentUsers = loadUsers()\n      const userToUpdate = Object.values(currentUsers).find(u => u.id === userId)\n\n      if (!userToUpdate) {\n        throw new Error(getErrorMessage('userNotFound', 'المستخدم غير موجود'))\n      }\n\n      // تحديث بيانات المستخدم\n      const updatedUser = {\n        ...userToUpdate,\n        ...userData,\n        id: userToUpdate.id, // الحفاظ على المعرف الأصلي\n        email: userData.email || userToUpdate.email, // الحفاظ على الإيميل إذا لم يتم تغييره\n        updatedAt: new Date().toISOString()\n      }\n\n      // حذف المستخدم القديم وإضافة المحدث\n      delete currentUsers[userToUpdate.email]\n      currentUsers[updatedUser.email] = updatedUser\n\n      // تحديث mockUsers\n      mockUsers = currentUsers\n\n      // حفظ التغييرات\n      saveUsers()\n\n      return {\n        message: getErrorMessage('userUpdated', 'تم تحديث المستخدم بنجاح'),\n        user: { ...updatedUser, password: undefined }\n      }\n    } catch (error) {\n      throw new Error(error.message || getErrorMessage('userUpdateFailed', 'فشل في تحديث المستخدم'))\n    }\n  },\n\n  // Get user details with password (admin only)\n  async getUserDetails(userId) {\n    await delay(500)\n\n    try {\n      // تحميل المستخدمين الحاليين\n      const currentUsers = loadUsers()\n      const user = Object.values(currentUsers).find(u => u.id === userId)\n\n      if (!user) {\n        throw new Error(getErrorMessage('userNotFound', 'المستخدم غير موجود'))\n      }\n\n      return user // إرجاع جميع البيانات بما في ذلك كلمة المرور\n    } catch (error) {\n      throw new Error(error.message || getErrorMessage('getUserDetailsFailed', 'فشل في جلب تفاصيل المستخدم'))\n    }\n  },\n\n  // Delete user (admin only)\n  async deleteUser(userId) {\n    await delay(500)\n\n    try {\n      // تحميل المستخدمين الحاليين\n      const currentUsers = loadUsers()\n      const userToDelete = Object.values(currentUsers).find(u => u.id === userId)\n\n      if (!userToDelete) {\n        throw new Error(getErrorMessage('userNotFound', 'المستخدم غير موجود'))\n      }\n\n      // منع حذف مدير النظام\n      if (userToDelete.role === 'admin') {\n        throw new Error(getErrorMessage('cannotDeleteAdmin', 'لا يمكن حذف مدير النظام'))\n      }\n\n      // حذف المستخدم\n      delete currentUsers[userToDelete.email]\n\n      // تحديث mockUsers\n      mockUsers = currentUsers\n\n      // حفظ التغييرات\n      saveUsers()\n\n      return { message: getErrorMessage('userDeleted', 'تم حذف المستخدم بنجاح') }\n    } catch (error) {\n      throw new Error(error.message || getErrorMessage('userDeleteFailed', 'فشل في حذف المستخدم'))\n    }\n  },\n\n  // Clear all data (reset system)\n  async clearAllData() {\n    await delay(500)\n\n    try {\n      // Clear ALL localStorage data\n      const keysToRemove = [\n        'app_users',\n        'app_courses',\n        'app_workshops',\n        'app_tasks',\n        'app_departments',\n        'app_rotations',\n        'app_reports',\n        'app_notifications',\n        'app_community_posts',\n        'app_achievements',\n        'app_badges',\n        'private_games',\n        'game_sessions',\n        'gamesPlayed',\n        'gamesWon',\n        'currentStreak',\n        'bestStreak',\n        'totalScore',\n        'playerRating',\n        'workshop_participants',\n        'workshop_groups',\n        'workshop_materials',\n        'task_submissions',\n        'course_enrollments',\n        'user_progress',\n        'system_settings',\n        'theme_preferences',\n        'language_preferences'\n      ]\n\n      // Remove all specified keys\n      keysToRemove.forEach(key => {\n        localStorage.removeItem(key)\n      })\n\n      // Clear any remaining app-related data\n      Object.keys(localStorage).forEach(key => {\n        if (key.startsWith('app_') || key.startsWith('game_') || key.startsWith('workshop_') || key.startsWith('course_') || key.startsWith('task_')) {\n          localStorage.removeItem(key)\n        }\n      })\n\n      // Reinitialize with default admin only\n      mockUsers = initializeUsers()\n\n      return { message: getErrorMessage('dataCleared', 'تم مسح جميع البيانات وإعادة تهيئة النظام') }\n    } catch (error) {\n      throw new Error(getErrorMessage('dataClearFailed', 'فشل في مسح البيانات'))\n    }\n  },\n\n  // Register new user\n  async register(userData) {\n    await delay(1000)\n\n    try {\n      const { name, email, password, role = 'trainee', phone, department, gender } = userData\n\n      // التحقق من البيانات المطلوبة\n      if (!name || !email || !password) {\n        throw new Error(getErrorMessage('namePasswordRequired', 'الاسم والبريد الإلكتروني وكلمة المرور مطلوبة'))\n      }\n\n      // تحميل المستخدمين الحاليين\n      const currentUsers = loadUsers()\n\n      // التحقق من عدم وجود المستخدم مسبقاً\n      if (currentUsers[email]) {\n        throw new Error(getErrorMessage('userExists', 'المستخدم موجود بالفعل'))\n      }\n\n      // إنشاء مستخدم جديد\n      const newUser = {\n        id: Date.now(),\n        name,\n        email,\n        username: email.split('@')[0], // استخدام الجزء الأول من البريد كاسم مستخدم\n        password,\n        role,\n        phone: phone || '',\n        department: department || '',\n        gender: gender || '',\n        avatar: null,\n        profileImage: null,\n        status: 'active',\n        isActive: true,\n        joinDate: new Date().toISOString(),\n        createdAt: new Date().toISOString(),\n        lastLogin: null\n      }\n\n      // إضافة المستخدم الجديد\n      currentUsers[email] = newUser\n      mockUsers = currentUsers\n\n      // حفظ البيانات\n      saveUsers()\n\n      // تسجيل حدث إنشاء المستخدم\n      logSecurityEvent('user_created', {\n        email: email,\n        name: name,\n        role: role,\n        timestamp: new Date().toISOString()\n      })\n\n      console.log('✅ تم إنشاء مستخدم جديد:', name)\n\n      return {\n        success: true,\n        message: 'تم إنشاء الحساب بنجاح',\n        user: { ...newUser, password: undefined }\n      }\n    } catch (error) {\n      console.error('❌ خطأ في إنشاء المستخدم:', error.message)\n      throw new Error(error.message || getErrorMessage('accountCreationFailed', 'فشل في إنشاء الحساب'))\n    }\n  },\n\n  async forgotPassword(email) {\n    // Simulate network delay\n    await delay(1000)\n\n    try {\n      // Check if email exists\n      const user = mockUsers[email]\n      if (!user) {\n        throw new Error('Email not found')\n      }\n\n      return { message: 'Password reset email sent successfully' }\n    } catch (error) {\n      throw new Error(error.message || 'Failed to send reset email')\n    }\n  },\n\n  async resetPassword(token, password) {\n    // Simulate network delay\n    await delay(1000)\n\n    try {\n      // Mock password reset\n      return { message: 'Password reset successfully' }\n    } catch (error) {\n      throw new Error('Failed to reset password')\n    }\n  },\n\n  async changePassword(currentPassword, newPassword) {\n    // Simulate network delay\n    await delay(1000)\n\n    try {\n      // Mock password change\n      return { message: 'Password changed successfully' }\n    } catch (error) {\n      throw new Error('Failed to change password')\n    }\n  },\n\n  async updateProfile(userData) {\n    // Simulate network delay\n    await delay(1000)\n\n    try {\n      // Mock profile update\n      return { ...userData, message: 'Profile updated successfully' }\n    } catch (error) {\n      throw new Error('Failed to update profile')\n    }\n  },\n\n  async uploadAvatar(file) {\n    // Simulate network delay\n    await delay(1500)\n\n    try {\n      // Mock avatar upload\n      const mockUrl = URL.createObjectURL(file)\n      return { avatar: mockUrl, message: 'Avatar uploaded successfully' }\n    } catch (error) {\n      throw new Error('Failed to upload avatar')\n    }\n  }\n}\n\n// Helper function to load users\nconst loadUsers = () => {\n  try {\n    const stored = localStorage.getItem('app_users')\n    return stored ? JSON.parse(stored) : {}\n  } catch (error) {\n    console.error('Error loading users:', error)\n    return {}\n  }\n}\n\nexport default authService\n"], "names": ["getCurrentLanguage", "getErrorMessage", "key", "fallback", "_a", "language", "logSecurityEvent", "eventType", "data", "logs", "newLog", "error", "initializeUsers", "stored", "existingUsers", "emptyUsers", "mockUsers", "ensureUsersIntegrity", "parsedUsers", "showUsersStatus", "forceResetUsers", "current", "storedUsers", "user", "fixLoginIssue", "saveUsers", "users", "needsRepair", "resetUserPassword", "emailOrName", "newPassword", "loadUsers", "foundUser", "u", "showAllUsersWithPasswords", "index", "currentData", "backup", "restoreError", "delay", "ms", "resolve", "authService", "userData", "email", "password", "name", "role", "userEmail", "currentUsers", "username", "newUser", "emailOrUsername", "savedUsers", "usernameMatch", "nameMatch", "emailMatch", "suggestions", "searchLower", "s", "userPassword", "token", "savedUser", "userId", "userToUpdate", "updatedUser", "userToDelete", "phone", "department", "gender", "currentPassword", "file"], "mappings": "AAIA,MAAMA,EAAqB,IAClB,aAAa,QAAQ,UAAU,GAAK,KAIvCC,EAAkB,CAACC,EAAKC,IAAa,CAT3C,IAAAC,EAUE,MAAMC,EAAWL,EAAkB,EA+CnC,QAAOI,EA9CU,CACf,GAAI,CACF,WAAY,sBACZ,qBAAsB,iCACtB,sBAAuB,2BACvB,cAAe,kBACf,iBAAkB,wBAClB,kBAAmB,qBACnB,YAAa,eACb,aAAc,gBACd,uBAAwB,6BACxB,kBAAmB,0BACnB,eAAgB,sBAChB,aAAc,iBACd,kBAAmB,2BACnB,iBAAkB,wBAClB,iBAAkB,wBAClB,qBAAsB,6BACtB,YAAa,4BACb,YAAa,4BACb,YAAa,4CACb,gBAAiB,sBACvB,EACI,GAAI,CACF,WAAY,wBACZ,qBAAsB,6BACtB,sBAAuB,sBACvB,cAAe,uCACf,iBAAkB,kBAClB,kBAAmB,wBACnB,YAAa,sBACb,aAAc,eACd,uBAAwB,iCACxB,kBAAmB,6BACnB,eAAgB,wBAChB,aAAc,qBACd,kBAAmB,0BACnB,iBAAkB,sBAClB,iBAAkB,wBAClB,qBAAsB,6BACtB,YAAa,wBACb,YAAa,0BACb,YAAa,2CACb,gBAAiB,qBACvB,CACA,EACkBC,CAAQ,IAAjB,YAAAD,EAAqBF,KAAQC,CACtC,EAGMG,EAAmB,CAACC,EAAWC,IAAS,CAC5C,GAAI,CACF,MAAMC,EAAO,KAAK,MAAM,aAAa,QAAQ,eAAe,GAAK,IAAI,EAC/DC,EAAS,CACb,GAAI,KAAK,IAAG,EACZ,KAAMH,EACN,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,KAAMC,EACN,UAAW,UAAU,UACrB,GAAI,WACV,EAEIC,EAAK,KAAKC,CAAM,EAGZD,EAAK,OAAS,KAChBA,EAAK,OAAO,EAAGA,EAAK,OAAS,GAAI,EAGnC,aAAa,QAAQ,gBAAiB,KAAK,UAAUA,CAAI,CAAC,EAC1D,QAAQ,IAAI,wBAAyBF,EAAWC,CAAI,CACtD,OAASG,EAAO,CACd,QAAQ,MAAM,2BAA4BA,CAAK,CACjD,CACF,EAGMC,EAAkB,IAAM,CAC5B,MAAMC,EAAS,aAAa,QAAQ,WAAW,EAC/C,GAAIA,EACF,GAAI,CACF,MAAMC,EAAgB,KAAK,MAAMD,CAAM,EACvC,eAAQ,IAAI,oCAAqC,OAAO,KAAKC,CAAa,CAAC,EACpEA,CACT,OAASH,EAAO,CACd,QAAQ,MAAM,gCAAiCA,CAAK,EACpD,aAAa,WAAW,WAAW,CACrC,CAGF,QAAQ,IAAI,mDAAmD,EAG/D,MAAMI,EAAa,CAAA,EACnB,oBAAa,QAAQ,YAAa,KAAK,UAAUA,CAAU,CAAC,EACrDA,CACT,EAEA,IAAIC,EAAYJ,EAAe,EAG/B,MAAMK,EAAuB,IAAM,CACjC,GAAI,CACF,MAAMJ,EAAS,aAAa,QAAQ,WAAW,EAC/C,GAAI,CAACA,EAAQ,CACX,QAAQ,IAAI,sCAAsC,EAClDG,EAAYJ,EAAe,EAC3B,MACF,CAEA,MAAMM,EAAc,KAAK,MAAML,CAAM,EACrC,GAAI,CAACK,GAAe,OAAOA,GAAgB,SAAU,CACnD,QAAQ,IAAI,0CAA0C,EACtDF,EAAYJ,EAAe,EAC3B,MACF,CAEAI,EAAYE,EACZ,QAAQ,IAAI,8BAA+B,OAAO,KAAKF,CAAS,EAAE,MAAM,CAC1E,OAASL,EAAO,CACd,QAAQ,MAAM,oCAAqCA,CAAK,EAExDK,EAAYJ,EAAe,CAC7B,CACF,EAGAK,EAAoB,EAGpB,QAAQ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAWX,EAGD,WAAW,IAAM,CACf,QAAQ,IAAI,yBAAyB,EACrCE,EAAe,CACjB,EAAG,GAAI,EAGP,MAAMC,EAAkB,IAAM,CAC5B,QAAQ,IAAI,mCAAmC,EAC/C,aAAa,WAAW,WAAW,EACnC,aAAa,WAAW,YAAY,EACpCJ,EAAYJ,EAAe,EAC3B,QAAQ,IAAI,6BAA8B,OAAO,KAAKI,CAAS,CAAC,CAClE,EAGA,OAAO,gBAAkBI,EAGzB,MAAMD,EAAkB,IAAM,CAC5B,MAAMN,EAAS,aAAa,QAAQ,WAAW,EACzCQ,EAAU,OAAO,KAAKL,CAAS,EAOrC,GALA,QAAQ,IAAI,qBAAqB,EACjC,QAAQ,IAAI,uBAAwBH,EAAS,OAAO,KAAK,KAAK,MAAMA,CAAM,CAAC,EAAE,OAAS,CAAC,EACvF,QAAQ,IAAI,oBAAqBQ,EAAQ,MAAM,EAC/C,QAAQ,IAAI,2BAA4BA,CAAO,EAE3CR,EAAQ,CACV,MAAMS,EAAc,KAAK,MAAMT,CAAM,EACrC,QAAQ,IAAI,4BAA6B,OAAO,KAAKS,CAAW,CAAC,EACjE,OAAO,OAAOA,CAAW,EAAE,QAAQC,GAAQ,CACzC,QAAQ,IAAI,SAASA,EAAK,IAAI,KAAKA,EAAK,KAAK,OAAOA,EAAK,IAAI,EAAE,CACjE,CAAC,CACH,CACF,EAEA,OAAO,gBAAkBJ,EAGzB,MAAMK,EAAgB,IAAM,CAC1B,QAAQ,IAAI,gCAAgC,EAG5C,MAAMX,EAAS,aAAa,QAAQ,WAAW,EAC/C,GAAI,CAACA,EAAQ,CACX,QAAQ,IAAI,0CAA0C,EACtDG,EAAYJ,EAAe,EAC3Ba,EAAS,EACT,MACF,CAEA,GAAI,CACF,MAAMC,EAAQ,KAAK,MAAMb,CAAM,EAC/B,GAAI,CAACa,GAAS,OAAO,KAAKA,CAAK,EAAE,SAAW,EAAG,CAC7C,QAAQ,IAAI,wCAAwC,EACpDV,EAAYJ,EAAe,EAC3Ba,EAAS,EACT,MACF,CAGA,IAAIE,EAAc,GAClB,OAAO,OAAOD,CAAK,EAAE,QAAQH,GAAQ,EAC/B,CAACA,EAAK,UAAY,CAACA,EAAK,WAAa,UACvCI,EAAc,GAElB,CAAC,EAEGA,IACF,QAAQ,IAAI,8BAA8B,EAC1C,OAAO,OAAOD,CAAK,EAAE,QAAQH,GAAQ,CAC9BA,EAAK,WAAUA,EAAK,SAAW,YAChCA,EAAK,WAAa,SAAWA,EAAK,SAAW,GACnD,CAAC,EACD,aAAa,QAAQ,YAAa,KAAK,UAAUG,CAAK,CAAC,GAGzDV,EAAYU,EACZ,QAAQ,IAAI,2CAA4C,OAAO,KAAKV,CAAS,EAAE,MAAM,CAEvF,OAASL,EAAO,CACd,QAAQ,MAAM,4BAA6BA,CAAK,EAChDK,EAAYJ,EAAe,EAC3Ba,EAAS,CACX,CACF,EAEA,OAAO,cAAgBD,EAGvB,MAAMI,EAAoB,CAACC,EAAaC,EAAc,aAAe,CACnE,GAAI,CACF,MAAMJ,EAAQK,EAAS,EACvB,IAAIR,EAAO,KAGX,GAAIM,EAAY,SAAS,GAAG,EAC1BN,EAAOG,EAAMG,CAAW,MACnB,CACL,MAAMG,EAAY,OAAO,OAAON,CAAK,EAAE,KAAKO,GAC1CA,EAAE,KAAK,gBAAkBJ,EAAY,YAAW,GAC/CI,EAAE,UAAYA,EAAE,SAAS,YAAW,IAAOJ,EAAY,YAAW,CAC3E,EACUG,IACFT,EAAOS,EAEX,CAEA,OAAKT,GAMLA,EAAK,SAAWO,EAChBJ,EAAMH,EAAK,KAAK,EAAIA,EACpBP,EAAYU,EAGZ,aAAa,QAAQ,YAAa,KAAK,UAAUA,CAAK,CAAC,EAEvD,QAAQ,IAAI,wBAAwBH,EAAK,IAAI,SAASO,CAAW,EAAE,EAC5D,KAbL,QAAQ,MAAM,wBAAyBD,CAAW,EAC3C,GAcX,OAASlB,EAAO,CACd,eAAQ,MAAM,oCAAqCA,CAAK,EACjD,EACT,CACF,EAEA,OAAO,kBAAoBiB,EAG3B,MAAMM,EAA4B,IAAM,CACtC,MAAMR,EAAQK,EAAS,EAIvB,GAHA,QAAQ,IAAI,qCAAqC,EACjD,QAAQ,IAAI,uCAAuC,EAE/C,OAAO,KAAKL,CAAK,EAAE,SAAW,EAAG,CACnC,QAAQ,IAAI,4BAA4B,EACxC,MACF,CAEA,OAAO,OAAOA,CAAK,EAAE,QAAQ,CAACH,EAAMY,IAAU,CAC5C,QAAQ,IAAI,GAAGA,EAAQ,CAAC,YAAYZ,EAAK,IAAI,EAAE,EAC/C,QAAQ,IAAI,cAAcA,EAAK,KAAK,EAAE,EACtC,QAAQ,IAAI,oBAAoBA,EAAK,UAAY,UAAU,EAAE,EAC7D,QAAQ,IAAI,mBAAmBA,EAAK,QAAQ,EAAE,EAC9C,QAAQ,IAAI,aAAaA,EAAK,IAAI,EAAE,EACpC,QAAQ,IAAI,YAAYA,EAAK,SAAW,MAAQ,IAAI,EAAE,EACtD,QAAQ,IAAI,QAAQ,CACtB,CAAC,EAED,QAAQ,IAAI,yBAAyB,OAAO,KAAKG,CAAK,EAAE,MAAM,EAAE,CAClE,EAEA,OAAO,0BAA4BQ,EAKnC,MAAMT,EAAY,IAAM,CACtB,GAAI,CAEF,MAAMW,EAAc,aAAa,QAAQ,WAAW,EAChDA,GACF,aAAa,QAAQ,mBAAoBA,CAAW,EAGtD,aAAa,QAAQ,YAAa,KAAK,UAAUpB,CAAS,CAAC,EAC3D,QAAQ,IAAI,8BAA+B,OAAO,KAAKA,CAAS,EAAE,MAAM,EAGtD,aAAa,QAAQ,WAAW,IAEhD,QAAQ,MAAM,wCAAwC,EAElDoB,GACF,aAAa,QAAQ,YAAaA,CAAW,EAGnD,OAASzB,EAAO,CACd,QAAQ,MAAM,wBAAyBA,CAAK,EAE5C,GAAI,CACF,MAAM0B,EAAS,aAAa,QAAQ,kBAAkB,EAClDA,IACF,aAAa,QAAQ,YAAaA,CAAM,EACxC,QAAQ,IAAI,yBAAyB,EAEzC,OAASC,EAAc,CACrB,QAAQ,MAAM,8BAA+BA,CAAY,CAC3D,CACF,CACF,EAEMC,EAASC,GAAO,IAAI,QAAQC,GAAW,WAAWA,EAASD,CAAE,CAAC,EAEvDE,EAAc,CACzB,MAAM,SAASC,EAAU,CACvB,MAAMJ,EAAM,GAAI,EAEhB,GAAI,CACF,KAAM,CAAE,MAAAK,EAAO,SAAAC,EAAU,KAAAC,EAAM,KAAAC,EAAO,SAAS,EAAKJ,EAG9CK,EAAYJ,GAAS,QAAQ,KAAK,IAAG,CAAE,gBAGvCK,EAAelB,EAAS,EAU9B,GAPI,OAAO,KAAKkB,CAAY,EAAE,SAAW,EACvCjC,EAAYJ,EAAe,EAE3BI,EAAYiC,EAIVL,GAAS5B,EAAUgC,CAAS,EAC9B,MAAM,IAAI,MAAM/C,EAAgB,aAAc,uBAAuB,CAAC,EAIxE,GAAI,CAAC4C,GAAY,CAACC,EAChB,MAAM,IAAI,MAAM7C,EAAgB,uBAAwB,4BAA4B,CAAC,EAIvF,MAAMiD,EAAWP,EAAS,UAAY,QAAQ,KAAK,KAAK,GAGlDQ,EAAU,CACd,GAAI,KAAK,IAAG,EACZ,MAAOH,EACP,SAAUE,EACV,KAAAJ,EACA,KAAAC,EACA,OAAQ,KACR,MAAOJ,EAAS,OAAS,GACzB,WAAYA,EAAS,YAAc,GACnC,OAAQA,EAAS,QAAU,GAC3B,OAAQ,SACR,SAAU,IAAI,KAAI,EAAG,YAAW,EAChC,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,SAAU,GACV,SAAAE,CACR,EAYM,GATA,QAAQ,IAAI,wBAAyBG,CAAS,EAC9ChC,EAAUgC,CAAS,EAAIG,EACvB,QAAQ,IAAI,iCAAkC,OAAO,KAAKnC,CAAS,EAAE,MAAM,EAG3ES,EAAS,EAIL,CADeM,EAAS,EACZiB,CAAS,EACvB,cAAQ,MAAM,8BAA8B,EACtC,IAAI,MAAM,4BAA4B,EAG9C,eAAQ,IAAI,kCAAmCA,CAAS,EAKjD,CACL,MAHY,kBAAkBG,EAAQ,EAAE,IAAI,KAAK,KAAK,GAItD,KAAM,CAAE,GAAGA,EAAS,SAAU,MAAS,CAC/C,CACI,OAASxC,EAAO,CACd,cAAQ,MAAM,yBAA0BA,EAAM,OAAO,EAC/C,IAAI,MAAMA,EAAM,SAAWV,EAAgB,wBAAyB,qBAAqB,CAAC,CAClG,CACF,EAEA,MAAM,MAAMmD,EAAiBP,EAAU,CACrC,MAAMN,EAAM,GAAI,EAEhB,GAAI,CAEFf,EAAa,EAGb,IAAI6B,EAAatB,EAAS,EAGtB,OAAO,KAAKsB,CAAU,EAAE,SAAW,GACrC,QAAQ,IAAI,oDAAoD,EAChErC,EAAYJ,EAAe,EAC3Ba,EAAS,EACT4B,EAAarC,GAEbA,EAAYqC,EAGd,QAAQ,IAAI,wBAAyBD,CAAe,EACpD,QAAQ,IAAI,0BAA2B,OAAO,KAAKpC,CAAS,CAAC,EAC7D,QAAQ,IAAI,qBAAsB,OAAO,KAAKA,CAAS,EAAE,MAAM,EAG/D,IAAIO,EAAO,KAkBX,GAfI6B,EAAgB,SAAS,GAAG,GAC9B7B,EAAOP,EAAUoC,CAAe,EAChC,QAAQ,IAAI,+BAAgC7B,EAAO,QAAU,WAAW,IAGxEA,EAAO,OAAO,OAAOP,CAAS,EAAE,KAAKiB,GAAK,CACxC,MAAMqB,EAAgBrB,EAAE,UAAYA,EAAE,SAAS,YAAW,IAAOmB,EAAgB,YAAW,EACtFG,EAAYtB,EAAE,MAAQA,EAAE,KAAK,YAAW,IAAOmB,EAAgB,YAAW,EAC1EI,EAAavB,EAAE,OAASA,EAAE,MAAM,YAAW,IAAOmB,EAAgB,YAAW,EAEnF,OAAOE,GAAiBC,GAAaC,CACvC,CAAC,EACD,QAAQ,IAAI,gCAAiCjC,EAAO,QAAU,WAAW,GAGvE,CAACA,EAAM,CACT,QAAQ,IAAI,qCAAqC,EACjD,QAAQ,IAAI,kBAAkB6B,CAAe,GAAG,EAChD,QAAQ,IAAI,kBAAkBA,EAAgB,SAAS,GAAG,EAAI,gBAAkB,gBAAgB,EAAE,EAClG,QAAQ,IAAI,gCAAgC,OAAO,KAAKpC,CAAS,EAAE,MAAM,EAAE,EAC3E,QAAQ,IAAI,0BAA0B,EACtC,OAAO,OAAOA,CAAS,EAAE,QAAQiB,GAAK,CACpC,QAAQ,IAAI,SAASA,EAAE,IAAI,MAAMA,EAAE,UAAY,oBAAoB,MAAMA,EAAE,KAAK,EAAE,CACpF,CAAC,EAGD,MAAMwB,EAAc,OAAO,OAAOzC,CAAS,EAAE,OAAOiB,GAAK,CACvD,MAAMyB,EAAcN,EAAgB,YAAW,EAC/C,OAAOnB,EAAE,KAAK,YAAW,EAAG,SAASyB,CAAW,GACzCzB,EAAE,MAAM,cAAc,SAASyB,CAAW,GACzCzB,EAAE,UAAYA,EAAE,SAAS,YAAW,EAAG,SAASyB,CAAW,CACrE,CAAC,EAED,MAAID,EAAY,OAAS,IACvB,QAAQ,IAAI,sBAAsB,EAClCA,EAAY,QAAQE,GAAK,CACvB,QAAQ,IAAI,SAASA,EAAE,IAAI,KAAKA,EAAE,KAAK,GAAG,CAC5C,CAAC,GAGG,IAAI,MAAM1D,EAAgB,gBAAiB,sCAAsC,CAAC,CAC1F,CAEA,GAAI,CAACsB,EAAK,SACR,MAAM,IAAI,MAAMtB,EAAgB,mBAAoB,iBAAiB,CAAC,EAIxEK,EAAiB,gBAAiB,CAChC,MAAO8C,EACP,QAAS,GACT,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,UAAW,UAAU,SAC7B,CAAO,EAGD,MAAMQ,EAAerC,EAAK,UAAY,WAGtC,GAAIsB,IAAae,EAEf,MAAAtD,EAAiB,uBAAwB,CACvC,MAAO8C,EACP,OAAQ,qBACR,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,UAAW,UAAU,SAC/B,CAAS,EAEK,IAAI,MAAMnD,EAAgB,oBAAqB,uBAAuB,CAAC,EAI/EsB,EAAK,UAAY,IAAI,KAAI,EAAG,YAAW,EACvCP,EAAUO,EAAK,KAAK,EAAIA,EACxBE,EAAS,EAGT,MAAMoC,EAAQ,kBAAkBtC,EAAK,EAAE,IAAI,KAAK,KAAK,GAGrD,oBAAa,QAAQ,QAASsC,CAAK,EACnC,aAAa,QAAQ,eAAgB,KAAK,UAAU,CAAE,GAAGtC,EAAM,SAAU,OAAW,CAAC,EACrF,aAAa,QAAQ,aAAc,IAAI,KAAI,EAAG,YAAW,CAAE,EAG3DjB,EAAiB,mBAAoB,CACnC,MAAO8C,EACP,OAAQ7B,EAAK,GACb,SAAUA,EAAK,KACf,KAAMA,EAAK,KACX,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,UAAW,UAAU,SAC7B,CAAO,EAED,QAAQ,IAAI,8BAA+BA,EAAK,IAAI,EAE7C,CACL,MAAAsC,EACA,KAAM,CAAE,GAAGtC,EAAM,SAAU,MAAS,CAC5C,CACI,OAASZ,EAAO,CACd,cAAQ,MAAM,yBAA0BA,EAAM,OAAO,EAC/C,IAAI,MAAMA,EAAM,SAAWV,EAAgB,cAAe,qBAAqB,CAAC,CACxF,CACF,EAEA,MAAM,QAAS,CAEb,MAAMsC,EAAM,GAAG,EAEf,GAAI,CAEF,oBAAa,WAAW,OAAO,EAC/B,aAAa,WAAW,cAAc,EACtC,aAAa,WAAW,YAAY,EAEpC,QAAQ,IAAI,yBAAyB,EAG9B,CAAE,QAAS,EAAI,CACxB,OAAS5B,EAAO,CACd,eAAQ,MAAM,yBAA0BA,CAAK,EACtC,CAAE,QAAS,EAAI,CACxB,CACF,EAEA,MAAM,gBAAiB,CACrB,MAAM4B,EAAM,GAAG,EAEf,GAAI,CACF,MAAMsB,EAAQ,aAAa,QAAQ,OAAO,EAC1C,GAAI,CAACA,GAAS,CAACA,EAAM,WAAW,gBAAgB,EAC9C,MAAM,IAAI,MAAM5D,EAAgB,eAAgB,cAAc,CAAC,EAIjEgB,EAAoB,EAGpB,MAAMoC,EAAatB,EAAS,EACxB,OAAO,KAAKsB,CAAU,EAAE,SAAW,GAErCrC,EAAYJ,EAAe,EAC3Ba,EAAS,GAETT,EAAYqC,EAId,MAAMS,EAAY,aAAa,QAAQ,cAAc,EACrD,GAAIA,EACF,GAAI,CACF,MAAMvC,EAAO,KAAK,MAAMuC,CAAS,EACjC,GAAIvC,GAAQA,EAAK,SACf,OAAOA,CAEX,OAASZ,EAAO,CACd,QAAQ,MAAM,yCAA0CA,CAAK,CAC/D,CAIF,MAAMoD,EAASF,EAAM,MAAM,GAAG,EAAE,CAAC,EAC3BtC,EAAO,OAAO,OAAOP,CAAS,EAAE,KAAKiB,GAAKA,EAAE,GAAG,SAAQ,IAAO8B,CAAM,EAE1E,GAAI,CAACxC,GAAQ,CAACA,EAAK,SACjB,MAAM,IAAI,MAAMtB,EAAgB,yBAA0B,gCAAgC,CAAC,EAI7F,oBAAa,QAAQ,eAAgB,KAAK,UAAU,CAAE,GAAGsB,EAAM,SAAU,OAAW,CAAC,EAE9E,CAAE,GAAGA,EAAM,SAAU,MAAS,CACvC,OAASZ,EAAO,CACd,MAAM,IAAI,MAAMA,EAAM,SAAWV,EAAgB,oBAAqB,4BAA4B,CAAC,CACrG,CACF,EAGA,MAAM,aAAc,CAClB,MAAMsC,EAAM,GAAG,EAEf,GAAI,CAEF,MAAMc,EAAatB,EAAS,EAC5B,eAAQ,IAAI,6CAA8C,OAAO,KAAKsB,CAAU,CAAC,EAG7E,OAAO,KAAKA,CAAU,EAAE,SAAW,GACrC,QAAQ,IAAI,0CAA0C,EACtDrC,EAAYJ,EAAe,EAC3Ba,EAAS,IAGT,QAAQ,IAAI,yDAA0D,OAAO,KAAKT,CAAS,CAAC,EAC5FA,EAAYqC,EACZ,QAAQ,IAAI,qCAAsC,OAAO,KAAKrC,CAAS,CAAC,GAGnE,OAAO,OAAOA,CAAS,EAAE,IAAIO,IAAS,CAC3C,GAAGA,EACH,SAAU,MAClB,EAAQ,CACJ,OAASZ,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtC,IAAI,MAAMV,EAAgB,iBAAkB,uBAAuB,CAAC,CAC5E,CACF,EAGA,MAAM,WAAW8D,EAAQpB,EAAU,CACjC,MAAMJ,EAAM,GAAG,EAEf,GAAI,CAEF,MAAMU,EAAelB,EAAS,EACxBiC,EAAe,OAAO,OAAOf,CAAY,EAAE,KAAKhB,GAAKA,EAAE,KAAO8B,CAAM,EAE1E,GAAI,CAACC,EACH,MAAM,IAAI,MAAM/D,EAAgB,eAAgB,oBAAoB,CAAC,EAIvE,MAAMgE,EAAc,CAClB,GAAGD,EACH,GAAGrB,EACH,GAAIqB,EAAa,GACjB,MAAOrB,EAAS,OAASqB,EAAa,MACtC,UAAW,IAAI,KAAI,EAAG,YAAW,CACzC,EAGM,cAAOf,EAAae,EAAa,KAAK,EACtCf,EAAagB,EAAY,KAAK,EAAIA,EAGlCjD,EAAYiC,EAGZxB,EAAS,EAEF,CACL,QAASxB,EAAgB,cAAe,yBAAyB,EACjE,KAAM,CAAE,GAAGgE,EAAa,SAAU,MAAS,CACnD,CACI,OAAStD,EAAO,CACd,MAAM,IAAI,MAAMA,EAAM,SAAWV,EAAgB,mBAAoB,uBAAuB,CAAC,CAC/F,CACF,EAGA,MAAM,eAAe8D,EAAQ,CAC3B,MAAMxB,EAAM,GAAG,EAEf,GAAI,CAEF,MAAMU,EAAelB,EAAS,EACxBR,EAAO,OAAO,OAAO0B,CAAY,EAAE,KAAKhB,GAAKA,EAAE,KAAO8B,CAAM,EAElE,GAAI,CAACxC,EACH,MAAM,IAAI,MAAMtB,EAAgB,eAAgB,oBAAoB,CAAC,EAGvE,OAAOsB,CACT,OAASZ,EAAO,CACd,MAAM,IAAI,MAAMA,EAAM,SAAWV,EAAgB,uBAAwB,4BAA4B,CAAC,CACxG,CACF,EAGA,MAAM,WAAW8D,EAAQ,CACvB,MAAMxB,EAAM,GAAG,EAEf,GAAI,CAEF,MAAMU,EAAelB,EAAS,EACxBmC,EAAe,OAAO,OAAOjB,CAAY,EAAE,KAAKhB,GAAKA,EAAE,KAAO8B,CAAM,EAE1E,GAAI,CAACG,EACH,MAAM,IAAI,MAAMjE,EAAgB,eAAgB,oBAAoB,CAAC,EAIvE,GAAIiE,EAAa,OAAS,QACxB,MAAM,IAAI,MAAMjE,EAAgB,oBAAqB,yBAAyB,CAAC,EAIjF,cAAOgD,EAAaiB,EAAa,KAAK,EAGtClD,EAAYiC,EAGZxB,EAAS,EAEF,CAAE,QAASxB,EAAgB,cAAe,uBAAuB,CAAC,CAC3E,OAASU,EAAO,CACd,MAAM,IAAI,MAAMA,EAAM,SAAWV,EAAgB,mBAAoB,qBAAqB,CAAC,CAC7F,CACF,EAGA,MAAM,cAAe,CACnB,MAAMsC,EAAM,GAAG,EAEf,GAAI,CAkCF,MAhCqB,CACnB,YACA,cACA,gBACA,YACA,kBACA,gBACA,cACA,oBACA,sBACA,mBACA,aACA,gBACA,gBACA,cACA,WACA,gBACA,aACA,aACA,eACA,wBACA,kBACA,qBACA,mBACA,qBACA,gBACA,kBACA,oBACA,sBACR,EAGmB,QAAQrC,GAAO,CAC1B,aAAa,WAAWA,CAAG,CAC7B,CAAC,EAGD,OAAO,KAAK,YAAY,EAAE,QAAQA,GAAO,EACnCA,EAAI,WAAW,MAAM,GAAKA,EAAI,WAAW,OAAO,GAAKA,EAAI,WAAW,WAAW,GAAKA,EAAI,WAAW,SAAS,GAAKA,EAAI,WAAW,OAAO,IACzI,aAAa,WAAWA,CAAG,CAE/B,CAAC,EAGDc,EAAYJ,EAAe,EAEpB,CAAE,QAASX,EAAgB,cAAe,0CAA0C,CAAC,CAC9F,MAAgB,CACd,MAAM,IAAI,MAAMA,EAAgB,kBAAmB,qBAAqB,CAAC,CAC3E,CACF,EAGA,MAAM,SAAS0C,EAAU,CACvB,MAAMJ,EAAM,GAAI,EAEhB,GAAI,CACF,KAAM,CAAE,KAAAO,EAAM,MAAAF,EAAO,SAAAC,EAAU,KAAAE,EAAO,UAAW,MAAAoB,EAAO,WAAAC,EAAY,OAAAC,GAAW1B,EAG/E,GAAI,CAACG,GAAQ,CAACF,GAAS,CAACC,EACtB,MAAM,IAAI,MAAM5C,EAAgB,uBAAwB,8CAA8C,CAAC,EAIzG,MAAMgD,EAAelB,EAAS,EAG9B,GAAIkB,EAAaL,CAAK,EACpB,MAAM,IAAI,MAAM3C,EAAgB,aAAc,uBAAuB,CAAC,EAIxE,MAAMkD,EAAU,CACd,GAAI,KAAK,IAAG,EACZ,KAAAL,EACA,MAAAF,EACA,SAAUA,EAAM,MAAM,GAAG,EAAE,CAAC,EAC5B,SAAAC,EACA,KAAAE,EACA,MAAOoB,GAAS,GAChB,WAAYC,GAAc,GAC1B,OAAQC,GAAU,GAClB,OAAQ,KACR,aAAc,KACd,OAAQ,SACR,SAAU,GACV,SAAU,IAAI,KAAI,EAAG,YAAW,EAChC,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,UAAW,IACnB,EAGM,OAAApB,EAAaL,CAAK,EAAIO,EACtBnC,EAAYiC,EAGZxB,EAAS,EAGTnB,EAAiB,eAAgB,CAC/B,MAAOsC,EACP,KAAME,EACN,KAAMC,EACN,UAAW,IAAI,KAAI,EAAG,YAAW,CACzC,CAAO,EAED,QAAQ,IAAI,0BAA2BD,CAAI,EAEpC,CACL,QAAS,GACT,QAAS,wBACT,KAAM,CAAE,GAAGK,EAAS,SAAU,MAAS,CAC/C,CACI,OAASxC,EAAO,CACd,cAAQ,MAAM,2BAA4BA,EAAM,OAAO,EACjD,IAAI,MAAMA,EAAM,SAAWV,EAAgB,wBAAyB,qBAAqB,CAAC,CAClG,CACF,EAEA,MAAM,eAAe2C,EAAO,CAE1B,MAAML,EAAM,GAAI,EAEhB,GAAI,CAGF,GAAI,CADSvB,EAAU4B,CAAK,EAE1B,MAAM,IAAI,MAAM,iBAAiB,EAGnC,MAAO,CAAE,QAAS,wCAAwC,CAC5D,OAASjC,EAAO,CACd,MAAM,IAAI,MAAMA,EAAM,SAAW,4BAA4B,CAC/D,CACF,EAEA,MAAM,cAAckD,EAAOhB,EAAU,CAEnC,MAAMN,EAAM,GAAI,EAEhB,GAAI,CAEF,MAAO,CAAE,QAAS,6BAA6B,CACjD,MAAgB,CACd,MAAM,IAAI,MAAM,0BAA0B,CAC5C,CACF,EAEA,MAAM,eAAe+B,EAAiBxC,EAAa,CAEjD,MAAMS,EAAM,GAAI,EAEhB,GAAI,CAEF,MAAO,CAAE,QAAS,+BAA+B,CACnD,MAAgB,CACd,MAAM,IAAI,MAAM,2BAA2B,CAC7C,CACF,EAEA,MAAM,cAAcI,EAAU,CAE5B,MAAMJ,EAAM,GAAI,EAEhB,GAAI,CAEF,MAAO,CAAE,GAAGI,EAAU,QAAS,8BAA8B,CAC/D,MAAgB,CACd,MAAM,IAAI,MAAM,0BAA0B,CAC5C,CACF,EAEA,MAAM,aAAa4B,EAAM,CAEvB,MAAMhC,EAAM,IAAI,EAEhB,GAAI,CAGF,MAAO,CAAE,OADO,IAAI,gBAAgBgC,CAAI,EACd,QAAS,8BAA8B,CACnE,MAAgB,CACd,MAAM,IAAI,MAAM,yBAAyB,CAC3C,CACF,CACF,EAGMxC,EAAY,IAAM,CACtB,GAAI,CACF,MAAMlB,EAAS,aAAa,QAAQ,WAAW,EAC/C,OAAOA,EAAS,KAAK,MAAMA,CAAM,EAAI,CAAA,CACvC,OAASF,EAAO,CACd,eAAQ,MAAM,uBAAwBA,CAAK,EACpC,CAAA,CACT,CACF"}