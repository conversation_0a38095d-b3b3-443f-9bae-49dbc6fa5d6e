// سكريبت لتنظيف البيانات الوهمية
(function() {
  console.log('🧹 بدء تنظيف البيانات الوهمية...');
  
  // قائمة البيانات المراد حذفها - شاملة لجميع البيانات التجريبية
  const dataKeys = [
    // ورش العمل
    'workshops_data',
    'workshop_participants', 
    'workshop_winners',
    'workshop_materials',
    'workshop_feedback',
    'workshop_certificates',
    
    // المهام
    'tasks_data',
    'task_assignments',
    'task_submissions',
    'task_grades',
    'task_feedback',
    'task_attachments',
    
    // الدورات والروتيشن
    'app_courses',
    'course_enrollments',
    'course_progress',
    'course_materials',
    'course_assignments',
    'course_grades',
    'rotation_data',
    'rotation_assignments',
    'rotation_schedules',
    'rotation_evaluations',
    
    // المجموعات
    'groups_data',
    'group_members',
    'group_activities',
    'group_discussions',
    
    // الحضور
    'attendance_data',
    'attendance_records',
    'attendance_reports',
    
    // التايم لاين
    'timeline_events',
    'timeline_data',
    'timeline_personal_tasks',
    'timeline_reminders',
    
    // التقارير
    'reports_data',
    'user_reports',
    'performance_reports',
    'progress_reports',
    
    // الإشعارات
    'notifications_data',
    'user_notifications',
    'notification_settings',
    
    // المجتمع
    'community_posts',
    'community_comments',
    'community_likes',
    'community_shares',
    
    // البيانات الإضافية
    'user_achievements',
    'user_certificates',
    'user_progress',
    'user_statistics',
    'system_logs',
    'activity_logs',
    'departments_data',
    'settings_data',
    'preferences_data',
    'chat_messages',
    'file_uploads',
    'exam_results',
    'quiz_results',
    'feedback_data',
    'evaluation_data',
    'calendar_events',
    'reminders_data',
    'bookmarks_data',
    'favorites_data'
  ];
  
  let deletedCount = 0;
  
  // حذف البيانات
  dataKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      deletedCount++;
      console.log(`✅ تم حذف: ${key}`);
    }
  });
  
  // إعادة تعيين المستخدمين (فقط Admin)
  const adminUser = {
    id: 1,
    name: 'Admin',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    avatar: null,
    password: 'admin123',
    phone: null,
    gender: 'ذكر',
    department: 'الإدارة',
    created_at: new Date().toISOString()
  };
  
  localStorage.setItem('app_users', JSON.stringify([adminUser]));
  console.log('👤 تم إعادة تعيين المستخدمين (Admin فقط)');
  
  console.log(`✅ تم تنظيف ${deletedCount} نوع من البيانات`);
  console.log('🔄 الموقع الآن كموقع جديد بدون بيانات وهمية');
  console.log('💡 يرجى إعادة تحميل الصفحة لرؤية التغييرات');
  
  // إعادة تحميل الصفحة بعد ثانيتين
  setTimeout(() => {
    window.location.reload();
  }, 2000);
})();