import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useLanguage } from '../../contexts/LanguageContext'
import { useAuth } from '../../contexts/AuthContext'
import {
  Calendar,
  Clock,
  CheckCircle,
  CalendarDays,
  RotateCw,
  Zap,
  CheckSquare,
  Plus,
  Download,
  Upload,
  Eye,
  Settings,
  GraduationCap
} from 'lucide-react'
import TraineeHeader from '../../components/trainee/TraineeHeader'
import StatsCard from '../../components/trainee/StatsCard'
import SearchAndFilter from '../../components/trainee/SearchAndFilter'
import Button from '../../components/ui/Button'
import toast from 'react-hot-toast'

const TimelinePage = () => {
  const { user } = useAuth()
  const { language, t } = useLanguage()
  const navigate = useNavigate()
  const [selectedTab, setSelectedTab] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [timelineEvents, setTimelineEvents] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalEvents: 0,
    totalRotations: 0,
    totalWorkshops: 0,
    totalTasks: 0,
    upcomingEvents: 0,
    completedEvents: 0
  })

  // دالة لاختيار الأيقونة المناسبة للحدث
  const getEventIcon = (event) => {
    const type = event.type || ''

    switch (type) {
      case 'rotation':
        return <GraduationCap className="w-5 h-5" />
      case 'workshop':
        return <Zap className="w-5 h-5" />
      case 'task':
        return <CheckSquare className="w-5 h-5" />
      default:
        return <Calendar className="w-5 h-5" />
    }
  }

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    loadTimelineData()
  }, [user])

  // دالة تنظيف البيانات الوهمية
  const cleanupFakeData = () => {
    try {
      console.log('🧹 بدء تنظيف البيانات الوهمية...')

      // تنظيف المهام الوهمية
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const realTasks = allTasks.filter(task => {
        // إزالة المهام التي تحتوي على نصوص وهمية أو مصدرها timeline
        const isFake = task.source === 'timeline' ||
                      task.trainer === 'مستورد من التايم لاين' ||
                      task.course === 'مستورد من التايم لاين' ||
                      task.description === 'مهمة مستوردة من التايم لاين'

        if (isFake) {
          console.log('🗑️ إزالة مهمة وهمية:', task.title)
        }
        return !isFake
      })
      localStorage.setItem('app_tasks', JSON.stringify(realTasks))

      // تنظيف ورش العمل الوهمية
      const allWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const realWorkshops = allWorkshops.filter(workshop => {
        const isFake = workshop.source === 'timeline' ||
                      workshop.trainer === 'مستورد من التايم لاين' ||
                      workshop.description === 'ورشة عمل مستوردة من التايم لاين'

        if (isFake) {
          console.log('🗑️ إزالة ورشة عمل وهمية:', workshop.title)
        }
        return !isFake
      })
      localStorage.setItem('app_workshops', JSON.stringify(realWorkshops))

      // تنظيف الدورات الوهمية
      const allCourses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      const realCourses = allCourses.filter(course => {
        const isFake = course.source === 'timeline' ||
                      course.instructor === 'مستورد من التايم لاين' ||
                      course.description === 'دورة مستوردة من التايم لاين'

        if (isFake) {
          console.log('🗑️ إزالة دورة وهمية:', course.title)
        }
        return !isFake
      })
      localStorage.setItem('app_courses', JSON.stringify(realCourses))

      console.log('✅ تم تنظيف البيانات الوهمية')
    } catch (error) {
      console.error('❌ خطأ في تنظيف البيانات الوهمية:', error)
    }
  }

  // تحميل بيانات التايم لاين
  const loadTimelineData = async () => {
    if (!user) return

    try {
      setLoading(true)
      console.log('🔄 بدء تحميل بيانات التايم لاين...')

      // تنظيف البيانات الوهمية أولاً
      cleanupFakeData()

      // تحميل البيانات من localStorage
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const allCourses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      const allWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')

      // فلترة البيانات للمتدرب الحالي
      const assignedTasks = allTasks.filter(task =>
        task.assignedUsers && task.assignedUsers.some(assignedUser =>
          (typeof assignedUser === 'object' && assignedUser.name === user.name) ||
          (typeof assignedUser === 'string' && assignedUser === user.name)
        )
      )

      const enrolledCourses = allCourses.filter(course =>
        course.selectedTrainees && course.selectedTrainees.some(trainee =>
          (typeof trainee === 'object' && trainee.name === user.name) ||
          (typeof trainee === 'string' && trainee === user.name)
        )
      )

      const participatingWorkshops = allWorkshops.filter(workshop =>
        workshop.selectedTrainees && workshop.selectedTrainees.some(trainee =>
          (typeof trainee === 'object' && trainee.name === user.name) ||
          (typeof trainee === 'string' && trainee === user.name)
        )
      )

      // تحويل البيانات إلى أحداث تايم لاين
      const events = []

      // إضافة المهام مع sourceId صحيح
      assignedTasks.forEach(task => {
        events.push({
          id: `task_${task.id}`,
          sourceId: task.id, // إضافة sourceId للتوجيه الصحيح
          title: task.title,
          description: task.description,
          type: 'task',
          date: task.dueDate,
          time: task.dueTime || '23:59',
          status: task.status || 'pending',
          priority: task.priority || 'medium',
          trainer: task.createdBy || 'غير محدد',
          category: 'task'
        })
      })

      // إضافة الدورات مع sourceId صحيح
      enrolledCourses.forEach(course => {
        events.push({
          id: `course_${course.id}`,
          sourceId: course.id, // إضافة sourceId للتوجيه الصحيح
          title: course.title,
          description: course.description,
          type: 'rotation',
          date: course.startDate,
          time: course.startTime || '09:00',
          status: course.status || 'active',
          trainer: course.instructor || 'غير محدد',
          category: 'course'
        })
      })

      // إضافة ورش العمل مع sourceId صحيح
      participatingWorkshops.forEach(workshop => {
        events.push({
          id: `workshop_${workshop.id}`,
          sourceId: workshop.id, // إضافة sourceId للتوجيه الصحيح
          title: workshop.title,
          description: workshop.description,
          type: 'workshop',
          date: workshop.startDate,
          time: workshop.startTime || '10:00',
          status: workshop.status || 'scheduled',
          trainer: workshop.trainer || 'غير محدد',
          category: 'workshop'
        })
      })

      // ترتيب الأحداث حسب التاريخ
      events.sort((a, b) => new Date(a.date) - new Date(b.date))

      // حساب الإحصائيات
      const now = new Date()
      const upcomingEvents = events.filter(event => {
        const eventDate = new Date(event.date)
        return eventDate > now && event.status !== 'completed'
      }).length
      const completedEvents = events.filter(event =>
        event.status === 'completed' || event.status === 'graded'
      ).length

      setStats({
        totalEvents: events.length,
        totalRotations: enrolledCourses.length,
        totalWorkshops: participatingWorkshops.length,
        totalTasks: assignedTasks.length,
        upcomingEvents,
        completedEvents
      })

      setTimelineEvents(events)
      setLoading(false)

      console.log('✅ تم تحميل بيانات التايم لاين بنجاح')

    } catch (error) {
      console.error('❌ خطأ في تحميل بيانات التايم لاين:', error)
      toast.error('حدث خطأ في تحميل البيانات')
      setLoading(false)
    }
  }

  // دالة الفلترة والبحث
  const getFilteredEvents = () => {
    let filtered = timelineEvents

    // فلترة حسب التبويب المحدد
    const now = new Date()
    if (selectedTab === 'upcoming') {
      filtered = filtered.filter(event => {
        const eventDate = new Date(event.date)
        return eventDate > now && event.status !== 'completed'
      })
    } else if (selectedTab === 'completed') {
      filtered = filtered.filter(event =>
        event.status === 'completed' || event.status === 'graded'
      )
    } else if (selectedTab === 'rotation') {
      filtered = filtered.filter(event => event.type === 'rotation')
    } else if (selectedTab === 'workshop') {
      filtered = filtered.filter(event => event.type === 'workshop')
    } else if (selectedTab === 'task') {
      filtered = filtered.filter(event => event.type === 'task')
    }

    // فلترة حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(event =>
        event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.trainer?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered.sort((a, b) => new Date(a.date) - new Date(b.date))
  }

  const filteredEvents = getFilteredEvents()

  // تبويبات الفلترة
  const tabs = [
    { id: 'all', label: t('timeline.all'), count: stats.totalEvents },
    { id: 'upcoming', label: t('timeline.upcomingEvents'), count: stats.upcomingEvents },
    { id: 'completed', label: t('timeline.completed'), count: stats.completedEvents },
    { id: 'rotation', label: t('timeline.courses'), count: stats.totalRotations },
    { id: 'workshop', label: t('timeline.workshops'), count: stats.totalWorkshops },
    { id: 'task', label: t('timeline.tasks'), count: stats.totalTasks }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <TraineeHeader
          title={t('timeline.title')}
          subtitle={t('timeline.subtitle')}
          icon={CalendarDays}
          iconBgColor="bg-purple-100 dark:bg-purple-900/30"
          iconColor="text-purple-600 dark:text-purple-400"
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
          <StatsCard
            icon={CalendarDays}
            value={stats.totalEvents}
            label={t('timeline.totalEvents')}
            subtitle={t('timeline.events')}
            iconBgColor="bg-purple-100 dark:bg-purple-900/30"
            iconColor="text-purple-600 dark:text-purple-400"
            onClick={() => setSelectedTab('all')}
            delay={0.1}
          />
          <StatsCard
            icon={RotateCw}
            value={stats.totalRotations}
            label={t('timeline.courses')}
            subtitle={t('timeline.registeredCourse')}
            iconBgColor="bg-blue-100 dark:bg-blue-900/30"
            iconColor="text-blue-600 dark:text-blue-400"
            onClick={() => navigate('/my-courses')}
            delay={0.2}
          />
          <StatsCard
            icon={Zap}
            value={stats.totalWorkshops}
            label={t('timeline.workshops')}
            subtitle={t('timeline.availableWorkshop')}
            iconBgColor="bg-green-100 dark:bg-green-900/30"
            iconColor="text-green-600 dark:text-green-400"
            onClick={() => navigate('/workshops')}
            delay={0.3}
          />
          <StatsCard
            icon={CheckSquare}
            value={stats.totalTasks}
            label={t('timeline.tasks')}
            subtitle={t('timeline.assignedTask')}
            iconBgColor="bg-orange-100 dark:bg-orange-900/30"
            iconColor="text-orange-600 dark:text-orange-400"
            onClick={() => navigate('/assignments')}
            delay={0.4}
          />
          <StatsCard
            icon={Clock}
            value={stats.upcomingEvents}
            label={t('timeline.upcomingEvents')}
            subtitle={t('timeline.event')}
            iconBgColor="bg-cyan-100 dark:bg-cyan-900/30"
            iconColor="text-cyan-600 dark:text-cyan-400"
            onClick={() => setSelectedTab('upcoming')}
            delay={0.5}
          />
          <StatsCard
            icon={CheckCircle}
            value={stats.completedEvents}
            label={t('timeline.completedEvents')}
            subtitle={t('timeline.completedEvent')}
            iconBgColor="bg-emerald-100 dark:bg-emerald-900/30"
            iconColor="text-emerald-600 dark:text-emerald-400"
            onClick={() => setSelectedTab('completed')}
            delay={0.6}
          />
        </div>

        {/* Search and Filter */}
        <SearchAndFilter
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabs={tabs}
          searchPlaceholder={t('timeline.search')}
        />

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Settings className="w-5 h-5 ml-2 text-purple-600" />
              {t('timeline.management')}
            </h3>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t('timeline.managementDesc')}
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            <Button
              variant="primary"
              onClick={() => toast.success(t('timeline.detailsComingSoon'))}
              icon={Plus}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {t('timeline.addPersonalTask')}
            </Button>

            <Button
              variant="outline"
              onClick={() => toast.success(t('timeline.detailsComingSoon'))}
              icon={Upload}
            >
              {t('timeline.importExcel')}
            </Button>

            <Button
              variant="success"
              onClick={() => toast.success(t('timeline.detailsComingSoon'))}
              icon={Download}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
            >
              {t('timeline.exportExcel')}
            </Button>
          </div>
        </motion.div>

        {/* Timeline Events */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">{t('timeline.loadingTimeline')}</span>
            </div>
          ) : filteredEvents.length === 0 ? (
            <div className="text-center py-16">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-gray-200 dark:border-gray-700 max-w-md mx-auto">
                <CalendarDays className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {t('timeline.noEvents')}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  {searchTerm
                    ? `${t('timeline.noEventsDesc')} "${searchTerm}"`
                    : selectedTab === 'all'
                      ? t('timeline.noEventsDesc')
                      : `${t('timeline.noEventsDesc')} ${selectedTab === 'upcoming' ? t('timeline.upcomingEvents') : selectedTab === 'completed' ? t('timeline.completed') : selectedTab}`
                  }
                </p>
                {searchTerm && (
                  <Button
                    onClick={() => setSearchTerm('')}
                    variant="outline"
                    size="sm"
                  >
                    {t('timeline.refresh')}
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                        <div className="text-purple-600 dark:text-purple-400">
                          {getEventIcon(event)}
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          {event.title}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {event.type === 'rotation' ? 'دورة تدريبية' :
                           event.type === 'workshop' ? 'ورشة عمل' :
                           event.type === 'task' ? 'مهمة' : 'حدث'}
                        </p>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {event.date}
                    </div>
                  </div>

                  {event.description && (
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      {event.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                        <Clock className="w-4 h-4 ml-1" />
                        {event.time || 'غير محدد'}
                      </span>
                      {event.status && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          event.status === 'completed' || event.status === 'graded'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : event.status === 'in_progress'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                        }`}>
                          {event.status === 'completed' || event.status === 'graded' ? 'مكتمل' :
                           event.status === 'in_progress' ? 'جاري' : 'معلق'}
                        </span>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      icon={Eye}
                      onClick={() => {
                        console.log('🖱️ تم الضغط على عرض التفاصيل:', {
                          title: event.title,
                          type: event.type,
                          id: event.id,
                          sourceId: event.sourceId
                        })

                        if (event.type === 'rotation') {
                          if (event.sourceId) {
                            console.log('🔄 التوجيه لتفاصيل الدورة:', event.sourceId)
                            navigate(`/course/${event.sourceId}`)
                          } else {
                            navigate('/my-courses')
                          }
                        } else if (event.type === 'workshop') {
                          if (event.sourceId) {
                            console.log('🔄 التوجيه لتفاصيل ورشة العمل:', event.sourceId)
                            navigate(`/trainee/workshop/${event.sourceId}`)
                          } else {
                            console.log('⚠️ لا يوجد sourceId لورشة العمل، التوجيه للصفحة العامة')
                            navigate('/workshop')
                          }
                        } else if (event.type === 'task') {
                          if (event.sourceId) {
                            console.log('🔄 التوجيه لتفاصيل المهمة:', event.sourceId)
                            navigate(`/task/${event.sourceId}`)
                          } else {
                            console.log('⚠️ لا يوجد sourceId للمهمة، التوجيه للصفحة العامة')
                            navigate('/assignments')
                          }
                        } else {
                          toast.success('سيتم إضافة صفحة التفاصيل قريباً')
                        }
                      }}
                    >
                      عرض التفاصيل
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default TimelinePage