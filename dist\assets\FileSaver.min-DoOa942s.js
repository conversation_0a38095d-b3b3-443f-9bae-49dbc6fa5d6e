import{c as j}from"./main-S9FuxvcD.js";function E(f,p){for(var c=0;c<p.length;c++){const a=p[c];if(typeof a!="string"&&!Array.isArray(a)){for(const s in a)if(s!=="default"&&!(s in f)){const l=Object.getOwnPropertyDescriptor(a,s);l&&Object.defineProperty(f,s,l.get?l:{enumerable:!0,get:()=>a[s]})}}}return Object.freeze(Object.defineProperty(f,Symbol.toStringTag,{value:"Module"}))}var h={exports:{}};(function(f,p){(function(c,a){a()})(j,function(){function c(e,t){return typeof t>"u"?t={autoBom:!1}:typeof t!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\uFEFF",e],{type:e.type}):e}function a(e,t,i){var o=new XMLHttpRequest;o.open("GET",e),o.responseType="blob",o.onload=function(){d(o.response,t,i)},o.onerror=function(){console.error("could not download file")},o.send()}function s(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return 200<=t.status&&299>=t.status}function l(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var r=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof globalThis=="object"&&globalThis.global===globalThis?globalThis:void 0,w=r.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),d=r.saveAs||(typeof window!="object"||window!==r?function(){}:"download"in HTMLAnchorElement.prototype&&!w?function(e,t,i){var o=r.URL||r.webkitURL,n=document.createElement("a");t=t||e.name||"download",n.download=t,n.rel="noopener",typeof e=="string"?(n.href=e,n.origin===location.origin?l(n):s(n.href)?a(e,t,i):l(n,n.target="_blank")):(n.href=o.createObjectURL(e),setTimeout(function(){o.revokeObjectURL(n.href)},4e4),setTimeout(function(){l(n)},0))}:"msSaveOrOpenBlob"in navigator?function(e,t,i){if(t=t||e.name||"download",typeof e!="string")navigator.msSaveOrOpenBlob(c(e,i),t);else if(s(e))a(e,t,i);else{var o=document.createElement("a");o.href=e,o.target="_blank",setTimeout(function(){l(o)})}}:function(e,t,i,o){if(o=o||open("","_blank"),o&&(o.document.title=o.document.body.innerText="downloading..."),typeof e=="string")return a(e,t,i);var n=e.type==="application/octet-stream",g=/constructor/i.test(r.HTMLElement)||r.safari,y=/CriOS\/[\d]+/.test(navigator.userAgent);if((y||n&&g||w)&&typeof FileReader<"u"){var v=new FileReader;v.onloadend=function(){var u=v.result;u=y?u:u.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=u:location=u,o=null},v.readAsDataURL(e)}else{var b=r.URL||r.webkitURL,m=b.createObjectURL(e);o?o.location=m:location.href=m,o=null,setTimeout(function(){b.revokeObjectURL(m)},4e4)}});r.saveAs=d.saveAs=d,f.exports=d})})(h);var O=h.exports;const A=E({__proto__:null},[O]);export{A as F};
//# sourceMappingURL=FileSaver.min-DoOa942s.js.map
