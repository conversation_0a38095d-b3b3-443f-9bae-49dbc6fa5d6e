// نظام مصادقة محسن مع باك اند محلي
import CryptoJS from 'crypto-js'

// مفتاح التشفير (في التطبيق الحقيقي يجب أن يكون في متغير بيئة)
const ENCRYPTION_KEY = 'training-platform-2024-secure-key'

// إعدادات النظام
const AUTH_CONFIG = {
  tokenExpiry: 24 * 60 * 60 * 1000, // 24 ساعة
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 دقيقة
  sessionTimeout: 2 * 60 * 60 * 1000, // ساعتان
  passwordMinLength: 6,
  requireStrongPassword: false
}

// قاعدة بيانات محلية للمستخدمين
class LocalDatabase {
  constructor() {
    this.storageKey = 'auth_users_db'
    this.sessionsKey = 'auth_sessions_db'
    this.securityKey = 'auth_security_db'
    this.initializeDatabase()
  }

  // تشفير البيانات
  encrypt(data) {
    try {
      return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString()
    } catch (error) {
      console.error('خطأ في التشفير:', error)
      return JSON.stringify(data) // fallback بدون تشفير
    }
  }

  // فك التشفير
  decrypt(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY)
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8))
    } catch (error) {
      console.error('خطأ في فك التشفير:', error)
      try {
        return JSON.parse(encryptedData) // fallback للبيانات غير المشفرة
      } catch {
        return null
      }
    }
  }

  // تهيئة قاعدة البيانات
  initializeDatabase() {
    try {
      // إنشاء المستخدمين الافتراضيين إذا لم توجد بيانات
      const existingUsers = this.getAllUsers()
      if (Object.keys(existingUsers).length === 0) {
        this.createDefaultUsers()
      }

      // تنظيف الجلسات المنتهية الصلاحية
      this.cleanExpiredSessions()
    } catch (error) {
      console.error('خطأ في تهيئة قاعدة البيانات:', error)
    }
  }

  // إنشاء المستخدمين الافتراضيين
  createDefaultUsers() {
    const defaultUsers = {
      '<EMAIL>': {
        id: 'admin_001',
        email: '<EMAIL>',
        username: 'admin',
        name: 'مدير النظام',
        password: this.hashPassword('admin123'),
        role: 'admin',
        avatar: null,
        phone: '',
        department: 'إدارة النظام',
        gender: '',
        status: 'active',
        isActive: true,
        joinDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        lastLogin: null,
        loginAttempts: 0,
        lockedUntil: null
      },
      '<EMAIL>': {
        id: 'trainer_001',
        email: '<EMAIL>',
        username: 'trainer',
        name: 'المدرب الرئيسي',
        password: this.hashPassword('trainer123'),
        role: 'trainer',
        avatar: null,
        phone: '',
        department: 'التدريب',
        gender: '',
        status: 'active',
        isActive: true,
        joinDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        lastLogin: null,
        loginAttempts: 0,
        lockedUntil: null
      },
      '<EMAIL>': {
        id: 'trainee_001',
        email: '<EMAIL>',
        username: 'trainee',
        name: 'المتدرب التجريبي',
        password: this.hashPassword('trainee123'),
        role: 'trainee',
        avatar: null,
        phone: '',
        department: 'التدريب',
        gender: '',
        status: 'active',
        isActive: true,
        joinDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        lastLogin: null,
        loginAttempts: 0,
        lockedUntil: null
      }
    }

    this.saveUsers(defaultUsers)
    console.log('✅ تم إنشاء المستخدمين الافتراضيين')
  }

  // تشفير كلمة المرور
  hashPassword(password) {
    return CryptoJS.SHA256(password + ENCRYPTION_KEY).toString()
  }

  // التحقق من كلمة المرور
  verifyPassword(password, hashedPassword) {
    return this.hashPassword(password) === hashedPassword
  }

  // حفظ المستخدمين
  saveUsers(users) {
    try {
      const encryptedData = this.encrypt(users)
      localStorage.setItem(this.storageKey, encryptedData)
      
      // حفظ نسخة احتياطية غير مشفرة للتوافق مع النظام الحالي
      localStorage.setItem('app_users', JSON.stringify(users))
    } catch (error) {
      console.error('خطأ في حفظ المستخدمين:', error)
    }
  }

  // تحميل المستخدمين
  getAllUsers() {
    try {
      const encryptedData = localStorage.getItem(this.storageKey)
      if (encryptedData) {
        return this.decrypt(encryptedData) || {}
      }

      // fallback للنظام القديم
      const oldData = localStorage.getItem('app_users')
      if (oldData) {
        const users = JSON.parse(oldData)
        // ترقية البيانات القديمة
        this.upgradeOldUsers(users)
        return users
      }

      return {}
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error)
      return {}
    }
  }

  // ترقية البيانات القديمة
  upgradeOldUsers(users) {
    try {
      let needsUpgrade = false
      Object.values(users).forEach(user => {
        // تشفير كلمات المرور غير المشفرة
        if (user.password && user.password.length < 50) { // كلمة مرور غير مشفرة
          user.password = this.hashPassword(user.password)
          needsUpgrade = true
        }
        
        // إضافة حقول الأمان المفقودة
        if (user.loginAttempts === undefined) {
          user.loginAttempts = 0
          needsUpgrade = true
        }
        if (user.lockedUntil === undefined) {
          user.lockedUntil = null
          needsUpgrade = true
        }
      })

      if (needsUpgrade) {
        this.saveUsers(users)
        console.log('✅ تم ترقية بيانات المستخدمين القديمة')
      }
    } catch (error) {
      console.error('خطأ في ترقية البيانات:', error)
    }
  }

  // البحث عن مستخدم
  findUser(emailOrUsername) {
    const users = this.getAllUsers()
    return Object.values(users).find(user => 
      user.email === emailOrUsername || 
      user.username === emailOrUsername
    )
  }

  // تحديث مستخدم
  updateUser(userId, updates) {
    try {
      const users = this.getAllUsers()
      const userKey = Object.keys(users).find(key => users[key].id === userId)
      
      if (userKey) {
        users[userKey] = { ...users[userKey], ...updates }
        this.saveUsers(users)
        return users[userKey]
      }
      
      return null
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error)
      return null
    }
  }

  // إدارة الجلسات
  createSession(user) {
    try {
      const sessionId = this.generateSessionId()
      const session = {
        id: sessionId,
        userId: user.id,
        userEmail: user.email,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + AUTH_CONFIG.tokenExpiry).toISOString(),
        lastActivity: new Date().toISOString(),
        userAgent: navigator.userAgent,
        ipAddress: 'localhost' // في التطبيق الحقيقي يتم الحصول على IP الحقيقي
      }

      const sessions = this.getAllSessions()
      sessions[sessionId] = session
      this.saveSessions(sessions)

      return sessionId
    } catch (error) {
      console.error('خطأ في إنشاء الجلسة:', error)
      return null
    }
  }

  // توليد معرف جلسة
  generateSessionId() {
    return CryptoJS.lib.WordArray.random(32).toString()
  }

  // حفظ الجلسات
  saveSessions(sessions) {
    try {
      const encryptedData = this.encrypt(sessions)
      localStorage.setItem(this.sessionsKey, encryptedData)
    } catch (error) {
      console.error('خطأ في حفظ الجلسات:', error)
    }
  }

  // تحميل الجلسات
  getAllSessions() {
    try {
      const encryptedData = localStorage.getItem(this.sessionsKey)
      if (encryptedData) {
        return this.decrypt(encryptedData) || {}
      }
      return {}
    } catch (error) {
      console.error('خطأ في تحميل الجلسات:', error)
      return {}
    }
  }

  // التحقق من صحة الجلسة
  validateSession(sessionId) {
    try {
      const sessions = this.getAllSessions()
      const session = sessions[sessionId]
      
      if (!session) {
        return null
      }

      // التحقق من انتهاء الصلاحية
      if (new Date() > new Date(session.expiresAt)) {
        delete sessions[sessionId]
        this.saveSessions(sessions)
        return null
      }

      // تحديث آخر نشاط
      session.lastActivity = new Date().toISOString()
      sessions[sessionId] = session
      this.saveSessions(sessions)

      return session
    } catch (error) {
      console.error('خطأ في التحقق من الجلسة:', error)
      return null
    }
  }

  // حذف جلسة
  deleteSession(sessionId) {
    try {
      const sessions = this.getAllSessions()
      delete sessions[sessionId]
      this.saveSessions(sessions)
    } catch (error) {
      console.error('خطأ في حذف الجلسة:', error)
    }
  }

  // تنظيف الجلسات المنتهية الصلاحية
  cleanExpiredSessions() {
    try {
      const sessions = this.getAllSessions()
      const now = new Date()
      let cleaned = false

      Object.keys(sessions).forEach(sessionId => {
        if (new Date(sessions[sessionId].expiresAt) < now) {
          delete sessions[sessionId]
          cleaned = true
        }
      })

      if (cleaned) {
        this.saveSessions(sessions)
        console.log('✅ تم تنظيف الجلسات المنتهية الصلاحية')
      }
    } catch (error) {
      console.error('خطأ في تنظيف الجلسات:', error)
    }
  }

  // تسجيل أحداث الأمان
  logSecurityEvent(eventType, details) {
    try {
      const securityLogs = this.getSecurityLogs()
      const event = {
        id: Date.now().toString(),
        type: eventType,
        timestamp: new Date().toISOString(),
        details: details,
        userAgent: navigator.userAgent
      }

      securityLogs.push(event)
      
      // الاحتفاظ بآخر 1000 حدث فقط
      if (securityLogs.length > 1000) {
        securityLogs.splice(0, securityLogs.length - 1000)
      }

      this.saveSecurityLogs(securityLogs)
    } catch (error) {
      console.error('خطأ في تسجيل حدث الأمان:', error)
    }
  }

  // حفظ سجلات الأمان
  saveSecurityLogs(logs) {
    try {
      const encryptedData = this.encrypt(logs)
      localStorage.setItem(this.securityKey, encryptedData)
    } catch (error) {
      console.error('خطأ في حفظ سجلات الأمان:', error)
    }
  }

  // تحميل سجلات الأمان
  getSecurityLogs() {
    try {
      const encryptedData = localStorage.getItem(this.securityKey)
      if (encryptedData) {
        return this.decrypt(encryptedData) || []
      }
      return []
    } catch (error) {
      console.error('خطأ في تحميل سجلات الأمان:', error)
      return []
    }
  }
}

// إنشاء instance من قاعدة البيانات
const db = new LocalDatabase()

// خدمة المصادقة المحسنة
export const enhancedAuthService = {
  // تسجيل الدخول
  async login(emailOrUsername, password) {
    try {
      console.log('🔐 بدء تسجيل الدخول المحسن:', emailOrUsername)

      // البحث عن المستخدم
      const user = db.findUser(emailOrUsername)
      if (!user) {
        db.logSecurityEvent('failed_login_attempt', {
          email: emailOrUsername,
          reason: 'user_not_found',
          timestamp: new Date().toISOString()
        })
        throw new Error('المستخدم غير موجود')
      }

      // التحقق من قفل الحساب
      if (user.lockedUntil && new Date() < new Date(user.lockedUntil)) {
        const remainingTime = Math.ceil((new Date(user.lockedUntil) - new Date()) / 60000)
        throw new Error(`الحساب مقفل. المحاولة مرة أخرى بعد ${remainingTime} دقيقة`)
      }

      // التحقق من كلمة المرور
      if (!db.verifyPassword(password, user.password)) {
        // زيادة عدد المحاولات الفاشلة
        const attempts = (user.loginAttempts || 0) + 1
        const updates = { loginAttempts: attempts }

        // قفل الحساب إذا تجاوز الحد المسموح
        if (attempts >= AUTH_CONFIG.maxLoginAttempts) {
          updates.lockedUntil = new Date(Date.now() + AUTH_CONFIG.lockoutDuration).toISOString()
        }

        db.updateUser(user.id, updates)
        
        db.logSecurityEvent('failed_login_attempt', {
          email: emailOrUsername,
          reason: 'incorrect_password',
          attempts: attempts,
          timestamp: new Date().toISOString()
        })

        throw new Error('كلمة المرور غير صحيحة')
      }

      // إعادة تعيين محاولات تسجيل الدخول الفاشلة
      db.updateUser(user.id, {
        loginAttempts: 0,
        lockedUntil: null,
        lastLogin: new Date().toISOString()
      })

      // إنشاء جلسة جديدة
      const sessionId = db.createSession(user)
      if (!sessionId) {
        throw new Error('فشل في إنشاء الجلسة')
      }

      // حفظ معلومات الجلسة في localStorage للتوافق
      localStorage.setItem('authToken', sessionId)
      localStorage.setItem('currentUser', JSON.stringify({
        ...user,
        password: undefined
      }))

      db.logSecurityEvent('successful_login', {
        email: emailOrUsername,
        userId: user.id,
        userName: user.name,
        role: user.role,
        timestamp: new Date().toISOString()
      })

      console.log('✅ تسجيل دخول ناجح:', user.name)

      return {
        success: true,
        user: { ...user, password: undefined },
        token: sessionId
      }

    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // تسجيل الخروج
  async logout() {
    try {
      const token = localStorage.getItem('authToken')
      if (token) {
        db.deleteSession(token)
      }

      // مسح بيانات الجلسة
      localStorage.removeItem('authToken')
      localStorage.removeItem('currentUser')
      localStorage.removeItem('token') // للتوافق مع النظام القديم

      console.log('✅ تم تسجيل الخروج بنجاح')
      return { success: true }
    } catch (error) {
      console.error('❌ خطأ في تسجيل الخروج:', error)
      return { success: false, error: error.message }
    }
  },

  // التحقق من صحة الرمز المميز
  async verifyToken(token) {
    try {
      const session = db.validateSession(token)
      if (!session) {
        return { success: false, error: 'جلسة غير صالحة' }
      }

      const user = db.findUser(session.userEmail)
      if (!user || !user.isActive) {
        return { success: false, error: 'المستخدم غير نشط' }
      }

      return {
        success: true,
        user: { ...user, password: undefined }
      }
    } catch (error) {
      console.error('❌ خطأ في التحقق من الرمز:', error)
      return { success: false, error: error.message }
    }
  },

  // التحقق من حالة المصادقة
  isAuthenticated() {
    try {
      const token = localStorage.getItem('authToken')
      if (!token) return false

      const session = db.validateSession(token)
      return !!session
    } catch (error) {
      console.error('❌ خطأ في التحقق من المصادقة:', error)
      return false
    }
  },

  // الحصول على المستخدم الحالي
  getCurrentUser() {
    try {
      const currentUser = localStorage.getItem('currentUser')
      if (currentUser) {
        return JSON.parse(currentUser)
      }
      return null
    } catch (error) {
      console.error('❌ خطأ في الحصول على المستخدم الحالي:', error)
      return null
    }
  },

  // الحصول على جميع المستخدمين
  async getAllUsers() {
    try {
      const users = db.getAllUsers()
      return Object.values(users).map(user => ({
        ...user,
        password: undefined
      }))
    } catch (error) {
      console.error('❌ خطأ في الحصول على المستخدمين:', error)
      return []
    }
  },

  // إنشاء مستخدم جديد
  async register(userData) {
    try {
      const { email, password, name, role = 'trainee' } = userData

      // التحقق من وجود المستخدم
      const existingUser = db.findUser(email)
      if (existingUser) {
        throw new Error('المستخدم موجود بالفعل')
      }

      // إنشاء المستخدم الجديد
      const newUser = {
        id: `${role}_${Date.now()}`,
        email,
        username: email.split('@')[0],
        name,
        password: db.hashPassword(password),
        role,
        avatar: null,
        phone: userData.phone || '',
        department: userData.department || '',
        gender: userData.gender || '',
        status: 'active',
        isActive: true,
        joinDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        lastLogin: null,
        loginAttempts: 0,
        lockedUntil: null
      }

      // حفظ المستخدم
      const users = db.getAllUsers()
      users[email] = newUser
      db.saveUsers(users)

      db.logSecurityEvent('user_registered', {
        email: email,
        name: name,
        role: role,
        timestamp: new Date().toISOString()
      })

      console.log('✅ تم إنشاء المستخدم بنجاح:', name)

      return {
        success: true,
        user: { ...newUser, password: undefined }
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء المستخدم:', error)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // تحديث المستخدم
  async updateUser(userId, updates) {
    try {
      // تشفير كلمة المرور الجديدة إذا تم تمريرها
      if (updates.password) {
        updates.password = db.hashPassword(updates.password)
      }

      const updatedUser = db.updateUser(userId, updates)
      if (!updatedUser) {
        throw new Error('فشل في تحديث المستخدم')
      }

      // تحديث المستخدم الحالي في localStorage إذا كان هو نفسه
      const currentUser = this.getCurrentUser()
      if (currentUser && currentUser.id === userId) {
        localStorage.setItem('currentUser', JSON.stringify({
          ...updatedUser,
          password: undefined
        }))
      }

      return {
        success: true,
        user: { ...updatedUser, password: undefined }
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث المستخدم:', error)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // الحصول على إحصائيات الأمان
  getSecurityStats() {
    try {
      const logs = db.getSecurityLogs()
      const sessions = db.getAllSessions()
      
      return {
        totalLogins: logs.filter(log => log.type === 'successful_login').length,
        failedAttempts: logs.filter(log => log.type === 'failed_login_attempt').length,
        activeSessions: Object.keys(sessions).length,
        lastActivity: logs.length > 0 ? logs[logs.length - 1].timestamp : null
      }
    } catch (error) {
      console.error('❌ خطأ في الحصول على إحصائيات الأمان:', error)
      return {
        totalLogins: 0,
        failedAttempts: 0,
        activeSessions: 0,
        lastActivity: null
      }
    }
  }
}

export default enhancedAuthService
