// Data configuration - Enhanced mode with improved backend
import { enhancedAuthService } from '../services/enhancedAuthService';
import { dataService as localDataService } from '../utils/dataStorage';

// Enhanced mode - use improved local backend
const USE_API = false; // Force localStorage for standalone version

console.log('🚀 Data Config - Enhanced Mode - Using improved local backend');

// Export the appropriate services - enhanced version
export const authService = enhancedAuthService; // Use the enhanced auth service
export const dataService = localDataService; // Always use localStorage
export const courseService = {
  // Fallback methods for localStorage mode
  async getCourses() {
    const courses = localDataService.getData('courses') || [];
    return { success: true, courses };
  },
  async createCourse(courseData) {
    const courses = localDataService.getData('courses') || [];
    const newCourse = {
      id: Date.now().toString(),
      ...courseData,
      created_at: new Date().toISOString()
    };
    courses.push(newCourse);
    localDataService.setData('courses', courses);
    return { success: true };
  }
};

export const workshopService = USE_API ? apiWorkshopService : {
  // Fallback methods for localStorage mode
  async getWorkshops() {
    const workshops = localDataService.getData('workshops') || [];
    return { success: true, workshops };
  },
  async createWorkshop(workshopData) {
    const workshops = localDataService.getData('workshops') || [];
    const newWorkshop = {
      id: Date.now().toString(),
      ...workshopData,
      created_at: new Date().toISOString()
    };
    workshops.push(newWorkshop);
    localDataService.setData('workshops', workshops);
    return { success: true };
  }
};

// Notification service - localStorage only
export const notificationService = {
  async getNotifications() {
    return localDataService.getData('notifications') || [];
  },
  
  async createNotification(notificationData) {
    const notifications = localDataService.getData('notifications') || [];
    const newNotification = {
      id: Date.now().toString(),
      ...notificationData,
      created_at: new Date().toISOString(),
      is_read: false
    };
    notifications.unshift(newNotification);
    localDataService.setData('notifications', notifications);
    return true;
  },
  
  async markAsRead(id) {
    const notifications = localDataService.getData('notifications') || [];
    const notification = notifications.find(n => n.id === id);
    if (notification) {
      notification.is_read = true;
      localDataService.setData('notifications', notifications);
      return true;
    }
    return false;
  }
};

export { USE_API };
export default {
  authService,
  dataService,
  courseService,
  workshopService,
  notificationService,
  USE_API
};