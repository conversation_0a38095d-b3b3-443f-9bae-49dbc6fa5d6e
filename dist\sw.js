// Service Worker للإشعارات في الوقت الفعلي
const CACHE_NAME = 'occ-trainee-v2'
const urlsToCache = [
  '/',
  '/manifest.json',
  '/favicon.ico'
]

// إعدادات الإشعارات
const NOTIFICATION_CONFIG = {
  icon: '/favicon.ico',
  badge: '/favicon.ico',
  requireInteraction: true,
  silent: false
}

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        // Add URLs one by one to handle failures gracefully
        return Promise.allSettled(
          urlsToCache.map(url => 
            cache.add(url).catch(err => {
              console.warn(`Failed to cache ${url}:`, err);
              return null;
            })
          )
        );
      })
      .then(() => {
        console.log('Service Worker installed successfully');
        self.skipWaiting();
      })
      .catch(err => {
        console.error('Service Worker installation failed:', err);
      })
  )
})

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
})

// التعامل مع الطلبات
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // إرجاع الملف من الكاش إذا وُجد، وإلا جلبه من الشبكة
        return response || fetch(event.request)
      })
  )
})

// التعامل مع الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SCHEDULE_REMINDER') {
    const { reminder, delay } = event.data
    
    // جدولة الإشعار
    setTimeout(() => {
      self.registration.showNotification(`تذكير: ${reminder.itemTitle}`, {
        body: `${reminder.label} - ${getItemTypeLabel(reminder.itemType)}`,
        ...NOTIFICATION_CONFIG,
        tag: `reminder_${reminder.itemId}`,
        actions: [
          {
            action: 'view',
            title: 'عرض التفاصيل'
          },
          {
            action: 'dismiss',
            title: 'تجاهل'
          }
        ],
        data: {
          reminder: reminder,
          url: `/${reminder.itemType}s/${reminder.itemId}`
        }
      })
    }, delay)
  }
})

// التعامل مع النقر على الإشعار
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  
  if (event.action === 'view') {
    // فتح التطبيق في الصفحة المناسبة
    const url = event.notification.data.url || '/'
    event.waitUntil(
      clients.openWindow(url)
    )
  } else if (event.action === 'dismiss') {
    // تجاهل الإشعار
    console.log('Notification dismissed')
  } else {
    // النقر على الإشعار نفسه
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

// دالة مساعدة للحصول على تسمية نوع العنصر
function getItemTypeLabel(itemType) {
  const labels = {
    task: 'مهمة',
    workshop: 'ورشة عمل',
    rotation: 'روتيشن',
    course: 'دورة'
  }
  return labels[itemType] || itemType
}

// فحص دوري للتذكيرات (كل دقيقة)
setInterval(() => {
  // إرسال رسالة للتطبيق الرئيسي للتحقق من التذكيرات
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({
        type: 'CHECK_REMINDERS'
      })
    })
  })
}, 60000) // كل دقيقة
