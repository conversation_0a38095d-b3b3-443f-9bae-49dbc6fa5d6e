const v=()=>localStorage.getItem("language")||"ar",n=(e,s)=>{var l;const r=v();return((l={en:{userExists:"User already exists",namePasswordRequired:"Name and password are required",accountCreationFailed:"Failed to create account",emailNotFound:"Email not found",accountNotActive:"Account is not active",incorrectPassword:"Incorrect password",loginFailed:"Login failed",invalidToken:"Invalid token",userNotFoundOrInactive:"User not found or inactive",getUserDataFailed:"Failed to get user data",getUsersFailed:"Failed to get users",userNotFound:"User not found",cannotDeleteAdmin:"Cannot delete admin user",userDeleteFailed:"Failed to delete user",userUpdateFailed:"Failed to update user",getUserDetailsFailed:"Failed to get user details",userDeleted:"User deleted successfully",userUpdated:"User updated successfully",dataCleared:"All data cleared and system reinitialized",dataClearFailed:"Failed to clear data"},ar:{userExists:"المستخدم موجود بالفعل",namePasswordRequired:"الاسم وكلمة المرور مطلوبان",accountCreationFailed:"فشل في إنشاء الحساب",emailNotFound:"الاسم أو البريد الإلكتروني غير موجود",accountNotActive:"الحساب غير مفعل",incorrectPassword:"كلمة المرور غير صحيحة",loginFailed:"فشل في تسجيل الدخول",invalidToken:"رمز غير صالح",userNotFoundOrInactive:"المستخدم غير موجود أو غير مفعل",getUserDataFailed:"فشل في جلب بيانات المستخدم",getUsersFailed:"فشل في جلب المستخدمين",userNotFound:"المستخدم غير موجود",cannotDeleteAdmin:"لا يمكن حذف مدير النظام",userDeleteFailed:"فشل في حذف المستخدم",userUpdateFailed:"فشل في تحديث المستخدم",getUserDetailsFailed:"فشل في جلب تفاصيل المستخدم",userDeleted:"تم حذف المستخدم بنجاح",userUpdated:"تم تحديث المستخدم بنجاح",dataCleared:"تم مسح جميع البيانات وإعادة تهيئة النظام",dataClearFailed:"فشل في مسح البيانات"}}[r])==null?void 0:l[e])||s},f=(e,s)=>{try{const r=JSON.parse(localStorage.getItem("security_logs")||"[]"),o={id:Date.now(),type:e,timestamp:new Date().toISOString(),data:s,userAgent:navigator.userAgent,ip:"localhost"};r.push(o),r.length>1e3&&r.splice(0,r.length-1e3),localStorage.setItem("security_logs",JSON.stringify(r)),console.log("🔒 تم تسجيل حدث أمان:",e,s)}catch(r){console.error("خطأ في تسجيل حدث الأمان:",r)}},g=()=>{const e=localStorage.getItem("app_users");if(e)try{const r=JSON.parse(e);return console.log("✅ Users loaded from localStorage:",Object.keys(r)),r}catch(r){console.error("❌ Error parsing stored users:",r),localStorage.removeItem("app_users")}console.log("🔄 No users found - starting with empty user base");const s={};return localStorage.setItem("app_users",JSON.stringify(s)),s};let t=g();const h=()=>{try{const e=localStorage.getItem("app_users");if(!e){console.log("🔄 No users found, starting fresh..."),t=g();return}const s=JSON.parse(e);if(!s||typeof s!="object"){console.log("🔄 Invalid users data, starting fresh..."),t=g();return}t=s,console.log("✅ Users integrity verified:",Object.keys(t).length)}catch(e){console.error("❌ Error checking users integrity:",e),t=g()}};h();console.log(`
🔧 دوال التشخيص والإصلاح المتاحة:
=====================================
• showUsersStatus() - عرض حالة المستخدمين
• showAllUsersWithPasswords() - عرض جميع المستخدمين مع كلمات المرور
• fixLoginIssue() - إصلاح مشاكل تسجيل الدخول
• resetUserPassword('email', 'newPassword') - إعادة تعيين كلمة المرور

• forceResetUsers() - إعادة تعيين جميع البيانات

مثال: resetUserPassword('<EMAIL>', '123456')
`);setTimeout(()=>{console.log("📊 حالة النظام الحالية:"),y()},1e3);const U=()=>{console.log("🔄 إعادة تعيين البيانات بالقوة..."),localStorage.removeItem("app_users"),localStorage.removeItem("users_data"),t=g(),console.log("✅ تم إعادة تعيين البيانات:",Object.keys(t))};window.forceResetUsers=U;const y=()=>{const e=localStorage.getItem("app_users"),s=Object.keys(t);if(console.log("📊 حالة المستخدمين:"),console.log("  - في localStorage:",e?Object.keys(JSON.parse(e)).length:0),console.log("  - في mockUsers:",s.length),console.log("  - المستخدمين الحاليين:",s),e){const r=JSON.parse(e);console.log("  - المستخدمين المحفوظين:",Object.keys(r)),Object.values(r).forEach(o=>{console.log(`    * ${o.name} (${o.email}) - ${o.role}`)})}};window.showUsersStatus=y;const S=()=>{console.log("🔧 إصلاح مشكلة تسجيل الدخول...");const e=localStorage.getItem("app_users");if(!e){console.log("🔧 لا توجد بيانات، إنشاء بيانات جديدة..."),t=g(),m();return}try{const s=JSON.parse(e);if(!s||Object.keys(s).length===0){console.log("🔧 بيانات فارغة، إنشاء بيانات جديدة..."),t=g(),m();return}let r=!1;Object.values(s).forEach(o=>{(!o.password||!o.isActive===void 0)&&(r=!0)}),r&&(console.log("🔧 إصلاح البيانات التالفة..."),Object.values(s).forEach(o=>{o.password||(o.password="password"),o.isActive===void 0&&(o.isActive=!0)}),localStorage.setItem("app_users",JSON.stringify(s))),t=s,console.log("✅ تم إصلاح المشكلة، المستخدمين المتاحين:",Object.keys(t).length)}catch(s){console.error("🔧 خطأ في إصلاح البيانات:",s),t=g(),m()}};window.fixLoginIssue=S;const _=(e,s="password")=>{try{const r=w();let o=null;if(e.includes("@"))o=r[e];else{const l=Object.values(r).find(a=>a.name.toLowerCase()===e.toLowerCase()||a.username&&a.username.toLowerCase()===e.toLowerCase());l&&(o=l)}return o?(o.password=s,r[o.email]=o,t=r,localStorage.setItem("app_users",JSON.stringify(r)),console.log(`✅ تم تغيير كلمة مرور ${o.name} إلى: ${s}`),!0):(console.error("❌ المستخدم غير موجود:",e),!1)}catch(r){return console.error("❌ خطأ في إعادة تعيين كلمة المرور:",r),!1}};window.resetUserPassword=_;const k=()=>{const e=w();if(console.log("👥 جميع المستخدمين مع كلمات المرور:"),console.log("====================================="),Object.keys(e).length===0){console.log("❌ لا توجد مستخدمين محفوظين");return}Object.values(e).forEach((s,r)=>{console.log(`${r+1}. الاسم: ${s.name}`),console.log(`   البريد: ${s.email}`),console.log(`   اسم المستخدم: ${s.username||"غير محدد"}`),console.log(`   كلمة المرور: ${s.password}`),console.log(`   الدور: ${s.role}`),console.log(`   مفعل: ${s.isActive?"نعم":"لا"}`),console.log("   ---")}),console.log(`📊 إجمالي المستخدمين: ${Object.keys(e).length}`)};window.showAllUsersWithPasswords=k;const m=()=>{try{const e=localStorage.getItem("app_users");e&&localStorage.setItem("app_users_backup",e),localStorage.setItem("app_users",JSON.stringify(t)),console.log("✅ Users saved successfully:",Object.keys(t).length),localStorage.getItem("app_users")||(console.error("❌ Failed to save users to localStorage"),e&&localStorage.setItem("app_users",e))}catch(e){console.error("❌ Error saving users:",e);try{const s=localStorage.getItem("app_users_backup");s&&(localStorage.setItem("app_users",s),console.log("🔄 Restored from backup"))}catch(s){console.error("❌ Failed to restore backup:",s)}}},i=e=>new Promise(s=>setTimeout(s,e)),O={async register(e){await i(1e3);try{const{email:s,password:r,name:o,role:l="trainee"}=e,a=s||`user_${Date.now()}@platform.com`,d=w();if(Object.keys(d).length===0?t=g():t=d,s&&t[a])throw new Error(n("userExists","المستخدم موجود بالفعل"));if(!r||!o)throw new Error(n("namePasswordRequired","الاسم وكلمة المرور مطلوبان"));const c=e.username||`user_${Date.now()}`,u={id:Date.now(),email:a,username:c,name:o,role:l,avatar:null,phone:e.phone||"",department:e.department||"",gender:e.gender||"",status:"active",joinDate:new Date().toISOString(),lastLogin:new Date().toISOString(),createdAt:new Date().toISOString(),isActive:!0,password:r};if(console.log("🔵 إضافة مستخدم جديد:",a),t[a]=u,console.log("🔵 عدد المستخدمين بعد الإضافة:",Object.keys(t).length),m(),!w()[a])throw console.error("❌ فشل في حفظ المستخدم الجديد"),new Error("فشل في حفظ بيانات المستخدم");return console.log("✅ تم حفظ المستخدم الجديد بنجاح:",a),{token:`mock-jwt-token-${u.id}-${Date.now()}`,user:{...u,password:void 0}}}catch(s){throw console.error("❌ خطأ في إنشاء الحساب:",s.message),new Error(s.message||n("accountCreationFailed","فشل في إنشاء الحساب"))}},async login(e,s){await i(1e3);try{S();let r=w();Object.keys(r).length===0?(console.log("🔄 لا توجد بيانات محفوظة، إنشاء بيانات افتراضية..."),t=g(),m(),r=t):t=r,console.log("🔍 البحث عن المستخدم:",e),console.log("🔍 المستخدمين المتاحين:",Object.keys(t)),console.log("🔍 عدد المستخدمين:",Object.keys(t).length);let o=null;if(e.includes("@")?(o=t[e],console.log("🔍 البحث بالبريد الإلكتروني:",o?"موجود":"غير موجود")):(o=Object.values(t).find(d=>{const c=d.username&&d.username.toLowerCase()===e.toLowerCase(),u=d.name&&d.name.toLowerCase()===e.toLowerCase(),p=d.email&&d.email.toLowerCase()===e.toLowerCase();return c||u||p}),console.log("🔍 البحث بالاسم/اسم المستخدم:",o?"موجود":"غير موجود")),!o){console.log("❌ المستخدم غير موجود. تفاصيل البحث:"),console.log(`  - البحث عن: "${e}"`),console.log(`  - نوع البحث: ${e.includes("@")?"بريد إلكتروني":"اسم مستخدم/اسم"}`),console.log(`  - عدد المستخدمين المتاحين: ${Object.keys(t).length}`),console.log("  - المستخدمين المتاحين:"),Object.values(t).forEach(c=>{console.log(`    * ${c.name} | ${c.username||"لا يوجد اسم مستخدم"} | ${c.email}`)});const d=Object.values(t).filter(c=>{const u=e.toLowerCase();return c.name.toLowerCase().includes(u)||c.email.toLowerCase().includes(u)||c.username&&c.username.toLowerCase().includes(u)});throw d.length>0&&(console.log("  - اقتراحات مشابهة:"),d.forEach(c=>{console.log(`    * ${c.name} (${c.email})`)})),new Error(n("emailNotFound","الاسم أو البريد الإلكتروني غير موجود"))}if(!o.isActive)throw new Error(n("accountNotActive","الحساب غير مفعل"));f("login_attempt",{email:e,success:!1,timestamp:new Date().toISOString(),userAgent:navigator.userAgent});const l=o.password||"password";if(s!==l)throw f("failed_login_attempt",{email:e,reason:"incorrect_password",timestamp:new Date().toISOString(),userAgent:navigator.userAgent}),new Error(n("incorrectPassword","كلمة المرور غير صحيحة"));o.lastLogin=new Date().toISOString(),t[o.email]=o,m();const a=`mock-jwt-token-${o.id}-${Date.now()}`;return localStorage.setItem("token",a),localStorage.setItem("current_user",JSON.stringify({...o,password:void 0})),localStorage.setItem("login_time",new Date().toISOString()),f("successful_login",{email:e,userId:o.id,userName:o.name,role:o.role,timestamp:new Date().toISOString(),userAgent:navigator.userAgent}),console.log("✅ تسجيل دخول ناجح للمستخدم:",o.name),{token:a,user:{...o,password:void 0}}}catch(r){throw console.error("❌ خطأ في تسجيل الدخول:",r.message),new Error(r.message||n("loginFailed","فشل في تسجيل الدخول"))}},async logout(){await i(500);try{return localStorage.removeItem("token"),localStorage.removeItem("current_user"),localStorage.removeItem("login_time"),console.log("✅ تم تسجيل الخروج بنجاح"),{success:!0}}catch(e){return console.error("❌ خطأ في تسجيل الخروج:",e),{success:!0}}},async getCurrentUser(){await i(500);try{const e=localStorage.getItem("token");if(!e||!e.startsWith("mock-jwt-token"))throw new Error(n("invalidToken","رمز غير صالح"));h();const s=w();Object.keys(s).length===0?(t=g(),m()):t=s;const r=localStorage.getItem("current_user");if(r)try{const a=JSON.parse(r);if(a&&a.isActive)return a}catch(a){console.error("خطأ في تحليل بيانات المستخدم المحفوظة:",a)}const o=e.split("-")[3],l=Object.values(t).find(a=>a.id.toString()===o);if(!l||!l.isActive)throw new Error(n("userNotFoundOrInactive","المستخدم غير موجود أو غير مفعل"));return localStorage.setItem("current_user",JSON.stringify({...l,password:void 0})),{...l,password:void 0}}catch(e){throw new Error(e.message||n("getUserDataFailed","فشل في جلب بيانات المستخدم"))}},async getAllUsers(){await i(500);try{const e=w();return console.log("🟢 getAllUsers - loaded from localStorage:",Object.keys(e)),Object.keys(e).length===0?(console.log("🟢 No saved users, initializing defaults"),t=g(),m()):(console.log("🟢 Using saved users, before assignment mockUsers has:",Object.keys(t)),t=e,console.log("🟢 After assignment mockUsers has:",Object.keys(t))),Object.values(t).map(s=>({...s,password:void 0}))}catch(e){throw console.error("Error in getAllUsers:",e),new Error(n("getUsersFailed","فشل في جلب المستخدمين"))}},async updateUser(e,s){await i(500);try{const r=w(),o=Object.values(r).find(a=>a.id===e);if(!o)throw new Error(n("userNotFound","المستخدم غير موجود"));const l={...o,...s,id:o.id,email:s.email||o.email,updatedAt:new Date().toISOString()};return delete r[o.email],r[l.email]=l,t=r,m(),{message:n("userUpdated","تم تحديث المستخدم بنجاح"),user:{...l,password:void 0}}}catch(r){throw new Error(r.message||n("userUpdateFailed","فشل في تحديث المستخدم"))}},async getUserDetails(e){await i(500);try{const s=w(),r=Object.values(s).find(o=>o.id===e);if(!r)throw new Error(n("userNotFound","المستخدم غير موجود"));return r}catch(s){throw new Error(s.message||n("getUserDetailsFailed","فشل في جلب تفاصيل المستخدم"))}},async deleteUser(e){await i(500);try{const s=w(),r=Object.values(s).find(o=>o.id===e);if(!r)throw new Error(n("userNotFound","المستخدم غير موجود"));if(r.role==="admin")throw new Error(n("cannotDeleteAdmin","لا يمكن حذف مدير النظام"));return delete s[r.email],t=s,m(),{message:n("userDeleted","تم حذف المستخدم بنجاح")}}catch(s){throw new Error(s.message||n("userDeleteFailed","فشل في حذف المستخدم"))}},async clearAllData(){await i(500);try{return["app_users","app_courses","app_workshops","app_tasks","app_departments","app_rotations","app_reports","app_notifications","app_community_posts","app_achievements","app_badges","private_games","game_sessions","gamesPlayed","gamesWon","currentStreak","bestStreak","totalScore","playerRating","workshop_participants","workshop_groups","workshop_materials","task_submissions","course_enrollments","user_progress","system_settings","theme_preferences","language_preferences"].forEach(s=>{localStorage.removeItem(s)}),Object.keys(localStorage).forEach(s=>{(s.startsWith("app_")||s.startsWith("game_")||s.startsWith("workshop_")||s.startsWith("course_")||s.startsWith("task_"))&&localStorage.removeItem(s)}),t=g(),{message:n("dataCleared","تم مسح جميع البيانات وإعادة تهيئة النظام")}}catch{throw new Error(n("dataClearFailed","فشل في مسح البيانات"))}},async register(e){await i(1e3);try{const{name:s,email:r,password:o,role:l="trainee",phone:a,department:d,gender:c}=e;if(!s||!r||!o)throw new Error(n("namePasswordRequired","الاسم والبريد الإلكتروني وكلمة المرور مطلوبة"));const u=w();if(u[r])throw new Error(n("userExists","المستخدم موجود بالفعل"));const p={id:Date.now(),name:s,email:r,username:r.split("@")[0],password:o,role:l,phone:a||"",department:d||"",gender:c||"",avatar:null,profileImage:null,status:"active",isActive:!0,joinDate:new Date().toISOString(),createdAt:new Date().toISOString(),lastLogin:null};return u[r]=p,t=u,m(),f("user_created",{email:r,name:s,role:l,timestamp:new Date().toISOString()}),console.log("✅ تم إنشاء مستخدم جديد:",s),{success:!0,message:"تم إنشاء الحساب بنجاح",user:{...p,password:void 0}}}catch(s){throw console.error("❌ خطأ في إنشاء المستخدم:",s.message),new Error(s.message||n("accountCreationFailed","فشل في إنشاء الحساب"))}},async forgotPassword(e){await i(1e3);try{if(!t[e])throw new Error("Email not found");return{message:"Password reset email sent successfully"}}catch(s){throw new Error(s.message||"Failed to send reset email")}},async resetPassword(e,s){await i(1e3);try{return{message:"Password reset successfully"}}catch{throw new Error("Failed to reset password")}},async changePassword(e,s){await i(1e3);try{return{message:"Password changed successfully"}}catch{throw new Error("Failed to change password")}},async updateProfile(e){await i(1e3);try{return{...e,message:"Profile updated successfully"}}catch{throw new Error("Failed to update profile")}},async uploadAvatar(e){await i(1500);try{return{avatar:URL.createObjectURL(e),message:"Avatar uploaded successfully"}}catch{throw new Error("Failed to upload avatar")}}},w=()=>{try{const e=localStorage.getItem("app_users");return e?JSON.parse(e):{}}catch(e){return console.error("Error loading users:",e),{}}};export{O as authService,O as default};
//# sourceMappingURL=authService-CRTpwTX-.js.map
