{"version": 3, "mappings": ";mEAGA,MAAMA,CAAgB,CACpB,aAAc,CACZ,KAAK,WAAa,sBAClB,KAAK,aAAe,gBAGpB,KAAK,eAAiB,CACpB,OACA,QACA,SACA,SACA,MACA,SACA,OACA,MACN,CACE,CAGA,eAAeC,EAAKC,EAAW,KAAM,OACnC,MAAMC,EAAkBD,GAAY,aAAa,QAAQ,UAAU,GAAK,KACxE,QAAOE,EAAAC,EAAqBF,CAAe,IAApC,YAAAC,EAAwCH,KAAQI,EAAqB,GAAGJ,CAAG,GAAKA,CACzF,CAGA,iBAAiBK,EAAQC,EAAOL,EAAW,KAAM,CAC/C,MAAMC,EAAkBD,GAAY,aAAa,QAAQ,UAAU,GAAK,KAExE,MAAO,GADkB,KAAK,eAAe,wBAAwBI,CAAM,GAAIH,CAAe,CACpE,IAAII,CAAK,EACrC,CAGA,uBAAuBC,EAAMN,EAAW,KAAM,CAC5C,MAAMC,EAAkBD,GAAY,aAAa,QAAQ,UAAU,GAAK,KACxE,OAAO,KAAK,eAAe,sBAAsBM,CAAI,GAAIL,CAAe,CAC1E,CAGA,2BAA4B,CAC1B,GAAI,CACF,MAAMM,EAAS,KAAK,kBAAiB,EACrC,IAAIC,EAAa,GAEjB,MAAMC,EAAgBF,EAAO,IAAIG,GAAS,CACxC,IAAIC,EAAWD,EAAM,MACjBE,EAAiBF,EAAM,YAG3B,GAAIA,EAAM,MAAM,SAAS,QAAQ,EAAG,CAClC,MAAMG,EAAYH,EAAM,MAAM,QAAQ,SAAU,EAAE,EAAE,KAAI,EACxDC,EAAW,KAAK,iBAAiB,QAASE,CAAS,EACnDL,EAAa,EACf,SAAWE,EAAM,MAAM,SAAS,QAAQ,EAAG,CACzC,MAAMG,EAAYH,EAAM,MAAM,QAAQ,SAAU,EAAE,EAAE,KAAI,EACxDC,EAAW,KAAK,iBAAiB,MAAOE,CAAS,EACjDL,EAAa,EACf,SAAWE,EAAM,MAAM,SAAS,QAAQ,GAAKA,EAAM,MAAM,SAAS,aAAa,GAAKA,EAAM,MAAM,SAAS,eAAe,EAAG,CACzH,MAAMG,EAAYH,EAAM,MAAM,QAAQ,mCAAoC,EAAE,EAAE,KAAI,EAClFC,EAAW,KAAK,iBAAiB,MAAOE,CAAS,EACjDL,EAAa,EACf,SAAWE,EAAM,MAAM,SAAS,WAAW,EAAG,CAC5C,MAAMG,EAAYH,EAAM,MAAM,QAAQ,YAAa,EAAE,EAAE,KAAI,EAC3DC,EAAW,MAAM,KAAK,iBAAiB,QAASE,CAAS,CAAC,GAC1DL,EAAa,EACf,SAAWE,EAAM,MAAM,SAAS,eAAe,EAAG,CAChD,MAAMG,EAAYH,EAAM,MAAM,QAAQ,gBAAiB,EAAE,EAAE,KAAI,EAC/DC,EAAW,KAAK,KAAK,iBAAiB,MAAOE,CAAS,CAAC,GACvDL,EAAa,EACf,CAGA,OAAIE,EAAM,cAAgB,sBACxBE,EAAiB,KAAK,uBAAuB,WAAW,EACxDJ,EAAa,IACJE,EAAM,cAAgB,sBAAwBA,EAAM,cAAgB,2BAC7EE,EAAiB,KAAK,uBAAuB,SAAS,EACtDJ,EAAa,IACJE,EAAM,cAAgB,0BAC/BE,EAAiB,KAAK,uBAAuB,eAAe,EAC5DJ,EAAa,IACJE,EAAM,cAAgB,0BAC/BE,EAAiB,KAAK,uBAAuB,aAAa,EAC1DJ,EAAa,IACJE,EAAM,cAAgB,sBAC/BE,EAAiB,KAAK,uBAAuB,aAAa,EAC1DJ,EAAa,IACJE,EAAM,cAAgB,sBAC/BE,EAAiB,KAAK,uBAAuB,WAAW,EACxDJ,EAAa,IACJE,EAAM,cAAgB,uBAAyBA,EAAM,cAAgB,2BAC9EE,EAAiB,KAAK,uBAAuB,eAAe,EAC5DJ,EAAa,IACJE,EAAM,cAAgB,4BAC/BE,EAAiB,KAAK,uBAAuB,aAAa,EAC1DJ,EAAa,IAGR,CACL,GAAGE,EACH,MAAOC,EACP,YAAaC,CACvB,CACM,CAAC,EAED,OAAIJ,GACF,KAAK,mBAAmBC,CAAa,EACrC,QAAQ,IAAI,4CAA4C,EACjD,IAGF,EACT,OAASK,EAAO,CACd,eAAQ,MAAM,iCAAkCA,CAAK,EAC9C,EACT,CACF,CAGA,wBAAyB,CACvB,QAAQ,IAAI,wCAAwC,EACpD,MAAMC,EAAU,KAAK,0BAAyB,EAC9C,OAAIA,IAEF,OAAO,cAAc,IAAI,MAAM,iBAAiB,CAAC,EACjD,OAAO,cAAc,IAAI,MAAM,6BAA6B,CAAC,GAExDA,CACT,CAGA,wBAAwBC,EAAWC,EAAW,CAC5C,GAAI,CAEF,MAAMC,EAAe,IAAI,KAAKF,CAAS,EAEvC,QAASG,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IAAK,CACzC,MAAMC,EAAWH,EAAUE,CAAC,EACtBE,EAAY,IAAI,KAAKD,EAAS,SAAS,EACvCE,EAAU,IAAI,KAAKF,EAAS,OAAO,EAEzC,GAAIF,GAAgBG,GAAaH,GAAgBI,EAE/C,OAAO,KAAK,eAAeH,EAAI,KAAK,eAAe,MAAM,CAE7D,CAGA,MAAO,MACT,OAASL,EAAO,CACd,eAAQ,MAAM,0BAA2BA,CAAK,EACvC,MACT,CACF,CAGA,MAAM,wBAAwBS,EAAM,CAClC,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtC,QAAQ,IAAI,4BAA6BF,EAAK,IAAI,EAClD,MAAMG,EAAS,IAAI,WAEnBA,EAAO,OAAUC,GAAM,CACrB,GAAI,CACF,QAAQ,IAAI,yBAAyB,EACrC,MAAMC,EAAO,IAAI,WAAWD,EAAE,OAAO,MAAM,EACrCE,EAAWC,EAAUF,EAAM,CAAE,KAAM,OAAO,CAAE,EAElD,QAAQ,IAAI,0BAA2BC,EAAS,UAAU,EAC1D,MAAME,EAAYF,EAAS,WAAW,CAAC,EACjCG,EAAYH,EAAS,OAAOE,CAAS,EACrCE,EAAWC,EAAW,cAAcF,CAAS,EAEnD,QAAQ,IAAI,yBAA0BC,EAAS,OAAQ,IAAI,EAC3D,QAAQ,IAAI,uBAAwBA,EAAS,MAAM,EAAG,CAAC,CAAC,EAExD,MAAM1B,EAAS,KAAK,eAAe0B,CAAQ,EAC3C,QAAQ,IAAI,aAAc1B,EAAO,OAAQ,KAAK,EAG9CiB,EAAQjB,CAAM,CAChB,OAASO,EAAO,CACd,QAAQ,MAAM,0BAA2BA,CAAK,EAC9CW,EAAOX,CAAK,CACd,CACF,EAEAY,EAAO,QAAU,IAAM,CACrB,QAAQ,MAAM,sBAAsB,EACpCD,EAAO,IAAI,MAAM,oBAAoB,CAAC,CACxC,EACAC,EAAO,kBAAkBH,CAAI,CAC/B,CAAC,CACH,CAGA,eAAeK,EAAM,CACnB,QAAQ,IAAI,8BAA8B,EAC1C,MAAMrB,EAAS,GACf,IAAI4B,EAAkB,KAClBC,EAAkB,EAEtB,GAAI,CAACR,GAAQA,EAAK,SAAW,EAC3B,eAAQ,KAAK,2BAA2B,EACjCrB,EAIT,MAAM8B,EAAU,OAAO,KAAKT,EAAK,CAAC,GAAK,EAAE,EACzC,eAAQ,IAAI,sBAAuBS,CAAO,EAE1CT,EAAK,QAAQ,CAACU,EAAKC,IAAU,CAC3B,GAAI,CAIF,GAHA,QAAQ,IAAI,iBAAiBA,EAAQ,CAAC,IAAKD,CAAG,EAG1C,CAACA,GAAO,OAAOA,GAAQ,SAAU,CACnC,QAAQ,IAAI,iCAAiC,EAC7C,MACF,CAGF,MAAME,EAAgB,OAAOF,EAAI,UAAYA,EAAI,UAAYA,EAAI,UACrCA,EAAI,QAAaA,EAAI,UAAe,EAAE,EAAE,KAAI,EAGlEG,EAAgB,OAAOH,EAAI,UAAYA,EAAI,UAAYA,EAAI,UACrCA,EAAI,YAAY,GAAKA,EAAI,WAAW,GAAK,EAAE,EAAE,KAAI,EAGvEI,EAAgB,OAAOJ,EAAI,kBAAkB,GAAKA,EAAI,YAChCA,EAAI,YAAY,GAAKA,EAAI,SAAc,EAAE,EAAE,KAAI,EAGrEK,EAAYL,EAAI,MAAQA,EAAI,MAAQA,EAAI,MAC7BA,EAAI,SAAcA,EAAI,OAAY,GAG7CM,EAAY,OAAON,EAAI,wCAAwC,GAC7CA,EAAI,MAAWA,EAAI,MAAQA,EAAI,OAAY,EAAE,EAAE,KAAI,EAK3E,GAHA,QAAQ,IAAI,mCAAmCE,CAAa,aAAaC,CAAa,aAAaC,CAAa,cAAcC,CAAS,YAAYC,CAAS,GAAG,EAG3JJ,GAAiB,OAAOA,GAAkB,UAAYA,EAAc,SAAS,mBAAmB,EAAG,CAErG,QAAQ,IAAI,wBAAyBA,CAAa,EAClD,MAAMK,EAAiB,KAAK,sBAAsBL,CAAa,EAC/DL,EAAkB,CAChB,GAAI,YAAYC,GAAiB,GACjC,OAAQS,EACR,KAAM,YAAYA,CAAc,GAChC,KAAM,iBACN,UAAW,KAAK,UAAUF,CAAS,EACnC,UAAW,EACrB,EAEQpC,EAAO,KAAK,CACV,GAAI4B,EAAgB,GACpB,MAAOA,EAAgB,KACvB,YAAa,SAASA,EAAgB,IAAI,GAC1C,KAAM,iBACN,KAAMA,EAAgB,UACtB,KAAM,QACN,SAAU,WACV,OAAQ,YACR,SAAUA,EACV,KAAM,KACN,MAAO,MACjB,CAAS,EACD,QAAQ,IAAI,2BAA4BA,EAAgB,IAAI,CAC9D,SAAWK,GAAiB,OAAOA,GAAkB,UAAYA,EAAc,SAAS,iBAAiB,GAGvG,GADA,QAAQ,IAAI,mBAAoBA,CAAa,EACzCL,EAAiB,CACnB,MAAMb,EAAU,KAAK,UAAUqB,CAAS,EACxCpC,EAAO,KAAK,CACV,GAAI,GAAG4B,EAAgB,EAAE,OACzB,MAAO,UAAUA,EAAgB,IAAI,GACrC,YAAa,SAASA,EAAgB,IAAI,GAC1C,KAAM,eACN,KAAMb,EACN,KAAM,QACN,SAAU,WACV,OAAQ,YACR,SAAUa,EACV,KAAM,IACN,MAAO,OACnB,CAAW,EACD,QAAQ,IAAI,2BAA4BA,EAAgB,IAAI,CAC9D,UACSM,GAAiB,OAAOA,GAAkB,UAAYA,EAAc,OAAS,EAAG,CAEzF,QAAQ,IAAI,eAAgBA,CAAa,EACzC,MAAMK,EAAW,CACf,GAAI,YAAY,KAAK,IAAG,CAAE,IAAIP,CAAK,GACnC,MAAOE,EACP,YAAaA,EACb,KAAM,WACN,KAAM,KAAK,UAAUE,CAAS,EAC9B,KAAM,KAAK,UAAUC,CAAS,EAC9B,SAAU,KAAK,kBAAkBA,CAAS,EAC1C,SAAU,WACV,OAAQ,YACR,WAAYT,EAAkBA,EAAgB,GAAK,KACnD,aAAcA,EAAkBA,EAAgB,KAAO,KACvD,KAAM,KACN,MAAO,QACjB,EAEYA,GAEFA,EAAgB,UAAU,KAAK,CAC7B,GAAIW,EAAS,GACb,MAAOA,EAAS,MAChB,KAAMA,EAAS,KACf,KAAMA,EAAS,IAC3B,CAAW,EAGHvC,EAAO,KAAKuC,CAAQ,EACpB,QAAQ,IAAI,uBAAwBL,CAAa,CACnD,MAAWC,GAAiB,OAAOA,GAAkB,UAAYA,EAAc,OAAS,GAEtF,QAAQ,IAAI,eAAgBA,CAAa,EACzCnC,EAAO,KAAK,CACV,GAAI,YAAY,KAAK,IAAG,CAAE,IAAIgC,CAAK,GACnC,MAAOG,EACP,YAAaA,EACb,KAAM,WACN,KAAM,KAAK,UAAUC,CAAS,EAC9B,KAAM,KAAK,UAAUC,CAAS,EAC9B,SAAU,KAAK,kBAAkBA,CAAS,EAC1C,SAAU,WACV,OAAQ,YACR,WAAYT,EAAkBA,EAAgB,GAAK,KACnD,aAAcA,EAAkBA,EAAgB,KAAO,KACvD,KAAM,KACN,MAAO,QACjB,CAAS,EACD,QAAQ,IAAI,mBAAoBO,CAAa,GAE7C,QAAQ,IAAI,2BAA4BJ,CAAG,CAE7C,OAASxB,EAAO,CACd,QAAQ,MAAM,uBAAuByB,EAAQ,CAAC,IAAKzB,EAAOwB,CAAG,CAE/D,CACF,CAAC,EAED,QAAQ,IAAI,6BAA8B/B,EAAO,OAAQ,KAAK,EACvDA,EAAO,KAAK,CAACwC,EAAGC,IAAM,IAAI,KAAKD,EAAE,IAAI,EAAI,IAAI,KAAKC,EAAE,IAAI,CAAC,CAClE,CAGA,sBAAsBC,EAAc,CAClC,GAAI,CAACA,GAAgB,OAAOA,GAAiB,SAC3C,eAAQ,IAAI,yBAA0BA,CAAY,EAC3C,EAGT,MAAMC,EAAW,CACf,kBACA,eACA,iBACA,OACN,EAEI,UAAWC,KAAWD,EAAU,CAC9B,MAAME,EAAQH,EAAa,MAAME,CAAO,EACxC,GAAIC,GAASA,EAAM,CAAC,EAAG,CACrB,MAAMC,EAAS,SAASD,EAAM,CAAC,CAAC,EAChC,eAAQ,IAAI,2BAA4BC,CAAM,EACvCA,CACT,CACF,CAEA,eAAQ,IAAI,4CAA4C,EACjD,CACT,CAGA,UAAUC,EAAS,CAGjB,GAFA,QAAQ,IAAI,oBAAqBA,CAAO,EAEpC,CAACA,EACH,eAAQ,IAAI,uCAAuC,EAC5C,IAAI,KAAI,EAAG,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,EAI9C,GAAI,OAAOA,GAAY,SAAU,CAC/B,QAAQ,IAAI,uBAAwBA,CAAO,EAE3C,MAAMC,EAAa,IAAI,KAAK,KAAM,EAAG,CAAC,EAEhCC,EADO,IAAI,KAAKD,EAAW,QAAO,GAAMD,EAAU,GAAK,GAAK,GAAK,GAAK,GAAI,EAC5D,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,EAC9C,eAAQ,IAAI,gBAAiBE,CAAM,EAC5BA,CACT,CAGA,MAAMC,EAAc,CAClB,gCACA,sBACA,gCACA,6BACN,EAEI,UAAWC,KAAUD,EAAa,CAChC,MAAML,EAAQE,EAAQ,SAAQ,EAAG,MAAMI,CAAM,EAC7C,GAAIN,GAGF,GAFA,QAAQ,IAAI,qBAAsBM,EAAQN,CAAK,EAE3CA,EAAM,SAAW,GAAKA,EAAM,CAAC,EAAG,CAElC,MAAMO,EAAM,SAASP,EAAM,CAAC,CAAC,EACvBQ,EAAQR,EAAM,CAAC,EACfS,EAAO,IAAI,KAAI,EAAG,YAAW,EAC7BC,EAAW,KAAK,eAAeF,CAAK,EACpCJ,EAAS,IAAI,KAAKK,EAAMC,EAAUH,CAAG,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,EACvE,eAAQ,IAAI,gBAAiBH,CAAM,EAC5BA,CACT,SAAWJ,EAAM,SAAW,EAAG,CAE7B,MAAMO,EAAM,SAASP,EAAM,CAAC,CAAC,EACvBQ,EAAQ,SAASR,EAAM,CAAC,CAAC,EAAI,EAC7BS,EAAO,SAAST,EAAM,CAAC,CAAC,EACxBI,EAAS,IAAI,KAAKK,EAAMD,EAAOD,CAAG,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,EACpE,eAAQ,IAAI,gBAAiBH,CAAM,EAC5BA,CACT,SAAWJ,EAAM,SAAW,EAAG,CAE7B,MAAMS,EAAO,SAAST,EAAM,CAAC,CAAC,EACxBQ,EAAQ,SAASR,EAAM,CAAC,CAAC,EAAI,EAC7BO,EAAM,SAASP,EAAM,CAAC,CAAC,EACvBI,EAAS,IAAI,KAAKK,EAAMD,EAAOD,CAAG,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,EACpE,eAAQ,IAAI,gBAAiBH,CAAM,EAC5BA,CACT,EAEJ,CAGA,GAAI,CACF,MAAMO,EAAO,IAAI,KAAKT,CAAO,EAC7B,GAAI,CAAC,MAAMS,EAAK,QAAO,CAAE,EAAG,CAC1B,MAAMP,EAASO,EAAK,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,EAC9C,eAAQ,IAAI,uBAAwBP,CAAM,EACnCA,CACT,CACF,OAAS1C,EAAO,CACd,QAAQ,KAAK,2BAA4BwC,EAASxC,CAAK,CACzD,CAEA,eAAQ,IAAI,iCAAiC,EACtC,IAAI,KAAI,EAAG,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAC9C,CAGA,UAAUkD,EAAS,CAGjB,GAFA,QAAQ,IAAI,kBAAmBA,CAAO,EAElC,CAACA,EACH,eAAQ,IAAI,4BAA4B,EACjC,QAIT,MAAMC,EAAe,CACnB,+BACA,oBACA,uBACA,sDACN,EAEI,UAAWd,KAAWc,EAAc,CAClC,MAAMb,EAAQY,EAAQ,SAAQ,EAAG,MAAMb,CAAO,EAC9C,GAAIC,EAAO,CACT,QAAQ,IAAI,yBAA0BD,EAASC,CAAK,EAEpD,IAAIc,EAAQ,SAASd,EAAM,CAAC,CAAC,EAC7B,MAAMe,EAAUf,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAI,KAChCgB,EAAShB,EAAM,CAAC,GAAKA,EAAM,CAAC,EAE9BgB,GAAUA,EAAO,YAAW,IAAO,MAAQF,IAAU,GACvDA,GAAS,GACAE,GAAUA,EAAO,YAAW,IAAO,MAAQF,IAAU,KAC9DA,EAAQ,GAGV,MAAMV,EAAS,GAAGU,EAAM,SAAQ,EAAG,SAAS,EAAG,GAAG,CAAC,IAAIC,CAAO,GAC9D,eAAQ,IAAI,cAAeX,CAAM,EAC1BA,CACT,CACF,CAEA,eAAQ,IAAI,kCAAkC,EACvC,OACT,CAGA,kBAAkBQ,EAAS,CACzB,GAAI,CAACA,EAAS,MAAO,KAErB,MAAMZ,EAAQY,EAAQ,MAAM,qDAAqD,EACjF,GAAIZ,EAAO,CACT,MAAMiB,EAAY,SAASjB,EAAM,CAAC,CAAC,EAC7BkB,EAAW,SAASlB,EAAM,CAAC,CAAC,EAClC,IAAImB,EAAU,SAASnB,EAAM,CAAC,CAAC,EAC/B,MAAMoB,EAAS,SAASpB,EAAM,CAAC,CAAC,EAE5BmB,IAAY,KAAIA,GAAW,IAE/B,MAAME,EAAeJ,EAAY,GAAKC,EAGtC,OAFmBC,EAAU,GAAKC,EAEdC,CACtB,CAEA,MAAO,IACT,CAGA,eAAeC,EAAW,CACxB,GAAI,CAACA,GAAa,OAAOA,GAAc,SACrC,eAAQ,IAAI,uBAAwBA,CAAS,EACtC,EAGT,MAAMC,EAAS,CAEb,IAAO,EAAG,IAAO,EAAG,IAAO,EAAG,IAAO,EAAG,IAAO,EAAG,IAAO,EACzD,IAAO,EAAG,IAAO,EAAG,IAAO,EAAG,IAAO,EAAG,IAAO,GAAI,IAAO,GAE1D,QAAW,EAAG,SAAY,EAAG,MAAS,EAAG,MAAS,EAAG,IAAO,EAAG,KAAQ,EACvE,KAAQ,EAAG,OAAU,EAAG,UAAa,EAAG,QAAW,EAAG,SAAY,GAAI,SAAY,GAElF,MAAS,EAAG,OAAU,EAAG,KAAQ,EAAG,MAAS,EAAG,KAAQ,EAAG,MAAS,EACpE,MAAS,EAAG,MAAS,EAAG,OAAU,EAAG,OAAU,EAAG,OAAU,GAAI,OAAU,EAChF,EAEUC,EAAW,OAAO,KAAKD,CAAM,EAAE,KAAK5E,GACxCA,EAAI,gBAAkB2E,EAAU,YAAW,CACjD,EAEUlB,EAASoB,EAAWD,EAAOC,CAAQ,EAAI,EAC7C,eAAQ,IAAI,kBAAmBF,EAAW,IAAKlB,CAAM,EAC9CA,CACT,CAGA,mBAAmBjD,EAAQ,CACzB,GAAI,CAEF,MAAMsE,EAActE,EAAO,IAAIG,GAAS,CACtC,MAAMoE,EAAa,CAAE,GAAGpE,CAAK,EAG7B,OAAIoE,EAAW,UAAY,OAAOA,EAAW,UAAa,WACxDA,EAAW,WAAaA,EAAW,SAAS,GAC5CA,EAAW,aAAeA,EAAW,SAAS,KAC9C,OAAOA,EAAW,UAIhBA,EAAW,eAAiB,MAAM,QAAQA,EAAW,aAAa,IACpEA,EAAW,cAAgBA,EAAW,cAAc,OACpD,OAAOA,EAAW,eAGhBA,EAAW,cAAgB,MAAM,QAAQA,EAAW,YAAY,IAClEA,EAAW,iBAAmBA,EAAW,aAAa,OACtD,OAAOA,EAAW,cAGbA,CACT,CAAC,EAED,aAAa,QAAQ,KAAK,WAAY,KAAK,UAAUD,CAAW,CAAC,EACjE,QAAQ,IAAI,WAAYA,EAAY,OAAQ,oBAAoB,EAGhE,MAAM5D,EAAYV,EAAO,OAAOG,GAASA,EAAM,OAAS,gBAAgB,EACrE,IAAIA,IAAU,CACb,GAAIA,EAAM,YAAcA,EAAM,GAC9B,KAAMA,EAAM,cAAgBA,EAAM,MAClC,UAAWA,EAAM,KACjB,UAAW,EACrB,EAAU,EACJ,aAAa,QAAQ,KAAK,aAAc,KAAK,UAAUO,CAAS,CAAC,CACnE,OAASH,EAAO,CACd,QAAQ,MAAM,gCAAiCA,CAAK,CACtD,CACF,CAGA,cAAe,CACb,GAAI,CAEF,oBAAa,WAAW,KAAK,UAAU,EACvC,aAAa,WAAW,KAAK,YAAY,EAGzC,aAAa,WAAW,iBAAiB,EACzC,aAAa,WAAW,oBAAoB,EAC5C,aAAa,WAAW,eAAe,EACvC,aAAa,WAAW,wBAAwB,EAGhD,aAAa,QAAQ,KAAK,WAAY,KAAK,UAAU,EAAE,CAAC,EACxD,aAAa,QAAQ,KAAK,aAAc,KAAK,UAAU,EAAE,CAAC,EAE1D,QAAQ,IAAI,yDAAyD,EAC9D,EACT,OAASA,EAAO,CACd,eAAQ,MAAM,yBAA0BA,CAAK,EACtC,EACT,CACF,CAGA,mBAAoB,CAClB,GAAI,CACF,MAAMiE,EAAS,aAAa,QAAQ,KAAK,UAAU,EACnD,OAAOA,EAAS,KAAK,MAAMA,CAAM,EAAI,EACvC,OAASjE,EAAO,CACd,eAAQ,MAAM,kCAAmCA,CAAK,EAC/C,EACT,CACF,CAGA,cAAe,CACb,MAAMiE,EAAS,aAAa,QAAQ,KAAK,YAAY,EACrD,OAAOA,EAAS,KAAK,MAAMA,CAAM,EAAI,EACvC,CAGA,yBAA0B,CACxB,GAAI,CACF,MAAMC,EAAY,KAAK,MAAM,aAAa,QAAQ,eAAe,GAAK,IAAI,EACpEzE,EAAS,KAAK,kBAAiB,EAErC,OAAAyE,EAAU,QAAQlC,GAAY,CAM5B,GAAI,CAJkBvC,EAAO,KAAKG,GAChCA,EAAM,OAAS,YAAcA,EAAM,WAAaoC,EAAS,EACnE,EAE4B,CAClB,MAAMmC,EAAgB,CACpB,GAAI,YAAYnC,EAAS,EAAE,GAC3B,SAAUA,EAAS,GACnB,MAAOA,EAAS,MAChB,YAAaA,EAAS,aAAeA,EAAS,MAC9C,KAAM,WACN,KAAMA,EAAS,KACf,KAAMA,EAAS,MAAQ,QACvB,SAAUA,EAAS,UAAY,IAC/B,SAAU,WACV,OAAQA,EAAS,QAAU,YAC3B,KAAM,KACN,MAAO,SACP,SAAUA,EAAS,SACnB,WAAYA,EAAS,WACrB,UAAW,IAAI,KAAI,EAAG,YAAW,CAC7C,EAEUvC,EAAO,KAAK0E,CAAa,EACzB,QAAQ,IAAI,mCAAoCnC,EAAS,KAAK,CAChE,CACF,CAAC,EAED,KAAK,mBAAmBvC,CAAM,EACvB,EACT,OAASO,EAAO,CACd,eAAQ,MAAM,6BAA8BA,CAAK,EAC1C,EACT,CACF,CAGA,qBAAsB,CACpB,GAAI,CACF,MAAMoE,EAAQ,KAAK,MAAM,aAAa,QAAQ,WAAW,GAAK,IAAI,EAC5D3E,EAAS,KAAK,kBAAiB,EAErC,OAAA2E,EAAM,QAAQC,GAAQ,CAMpB,GAAI,CAJkB5E,EAAO,KAAKG,GAChCA,EAAM,OAAS,QAAUA,EAAM,WAAayE,EAAK,EAC3D,EAE4B,CAClB,MAAMC,EAAY,CAChB,GAAI,QAAQD,EAAK,EAAE,GACnB,SAAUA,EAAK,GACf,MAAOA,EAAK,MACZ,YAAaA,EAAK,aAAeA,EAAK,MACtC,KAAM,OACN,KAAMA,EAAK,SAAWA,EAAK,KAC3B,KAAMA,EAAK,MAAQ,QACnB,SAAUA,EAAK,eAAiB,GAChC,SAAU,OACV,OAAQA,EAAK,QAAU,UACvB,SAAUA,EAAK,UAAY,SAC3B,KAAM,KACN,MAAOA,EAAK,WAAa,OAAS,MAAQA,EAAK,WAAa,SAAW,SAAW,OAClF,WAAYA,EAAK,WACjB,UAAW,IAAI,KAAI,EAAG,YAAW,CAC7C,EAEU5E,EAAO,KAAK6E,CAAS,EACrB,QAAQ,IAAI,+BAAgCD,EAAK,KAAK,CACxD,CACF,CAAC,EAED,KAAK,mBAAmB5E,CAAM,EACvB,EACT,OAASO,EAAO,CACd,eAAQ,MAAM,0BAA2BA,CAAK,EACvC,EACT,CACF,CAGA,sBAAsBgC,EAAU,CAC9B,GAAI,CACF,MAAMvC,EAAS,KAAK,kBAAiB,EAE/B0E,EAAgB,CACpB,GAAI,YAAYnC,EAAS,EAAE,GAC3B,SAAUA,EAAS,GACnB,MAAOA,EAAS,MAChB,YAAaA,EAAS,aAAeA,EAAS,MAC9C,KAAM,WACN,KAAMA,EAAS,KACf,KAAMA,EAAS,MAAQ,QACvB,SAAUA,EAAS,UAAY,IAC/B,SAAU,WACV,OAAQA,EAAS,QAAU,YAC3B,KAAM,KACN,MAAO,SACP,SAAUA,EAAS,SACnB,WAAYA,EAAS,WACrB,UAAW,IAAI,KAAI,EAAG,YAAW,CACzC,EAEM,OAAAvC,EAAO,KAAK0E,CAAa,EACzB,KAAK,mBAAmB1E,CAAM,EAC9B,QAAQ,IAAI,yCAA0CuC,EAAS,KAAK,EAC7D,EACT,OAAShC,EAAO,CACd,eAAQ,MAAM,yCAA0CA,CAAK,EACtD,EACT,CACF,CAGA,kBAAkBqE,EAAM,CACtB,GAAI,CACF,MAAM5E,EAAS,KAAK,kBAAiB,EAE/B6E,EAAY,CAChB,GAAI,QAAQD,EAAK,EAAE,GACnB,SAAUA,EAAK,GACf,MAAOA,EAAK,MACZ,YAAaA,EAAK,aAAeA,EAAK,MACtC,KAAM,OACN,KAAMA,EAAK,SAAWA,EAAK,KAC3B,KAAMA,EAAK,MAAQ,QACnB,SAAUA,EAAK,eAAiB,GAChC,SAAU,OACV,OAAQA,EAAK,QAAU,UACvB,SAAUA,EAAK,UAAY,SAC3B,KAAM,KACN,MAAOA,EAAK,WAAa,OAAS,MAAQA,EAAK,WAAa,SAAW,SAAW,OAClF,WAAYA,EAAK,WACjB,UAAW,IAAI,KAAI,EAAG,YAAW,CACzC,EAEM,OAAA5E,EAAO,KAAK6E,CAAS,EACrB,KAAK,mBAAmB7E,CAAM,EAC9B,QAAQ,IAAI,qCAAsC4E,EAAK,KAAK,EACrD,EACT,OAASrE,EAAO,CACd,eAAQ,MAAM,qCAAsCA,CAAK,EAClD,EACT,CACF,CAGA,qBAAqBuE,EAAQ,CAe3B,MAdoB,CAClB,QAAW,cACX,OAAU,MACV,UAAa,QACb,UAAa,OACb,YAAe,cACf,UAAa,QACb,UAAa,QACb,MAAS,QACT,UAAa,aACb,OAAU,aACV,QAAW,QACX,OAAU,MAChB,EACuBA,CAAM,GAAKA,GAAU,UAC1C,CAGA,SAAS3E,EAAO,CACd,MAAMH,EAAS,KAAK,kBAAiB,EAG/B+E,EAAgB/E,EAAO,KAAKoB,GAChCA,EAAE,WAAajB,EAAM,UACrBiB,EAAE,OAASjB,EAAM,MACjBiB,EAAE,OAASjB,EAAM,IACvB,EAEI,GAAI4E,EACF,eAAQ,IAAI,wCAAyC5E,EAAM,KAAK,EACzD4E,EAGT,MAAMC,EAAW,CACf,GAAG7E,EACH,GAAIA,EAAM,IAAM,SAAS,KAAK,IAAG,CAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GAC9E,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,UAAW,IAAI,KAAI,EAAG,YAAW,CACvC,EAEI,OAAAH,EAAO,KAAKgF,CAAQ,EACpBhF,EAAO,KAAK,CAACwC,EAAGC,IAAM,IAAI,KAAKD,EAAE,IAAI,EAAI,IAAI,KAAKC,EAAE,IAAI,CAAC,EACzD,KAAK,mBAAmBzC,CAAM,EAE9B,QAAQ,IAAI,mCAAoCgF,EAAS,KAAK,EACvDA,CACT,CAGA,YAAYC,EAASC,EAAS,CAC5B,MAAMlF,EAAS,KAAK,kBAAiB,EAC/BgC,EAAQhC,EAAO,UAAUG,GAASA,EAAM,KAAO8E,CAAO,EAE5D,OAAIjD,IAAU,IACZhC,EAAOgC,CAAK,EAAI,CACd,GAAGhC,EAAOgC,CAAK,EACf,GAAGkD,EACH,UAAW,IAAI,KAAI,EAAG,YAAW,CACzC,EAEMlF,EAAO,KAAK,CAACwC,EAAGC,IAAM,IAAI,KAAKD,EAAE,IAAI,EAAI,IAAI,KAAKC,EAAE,IAAI,CAAC,EACzD,KAAK,mBAAmBzC,CAAM,EAEvBA,EAAOgC,CAAK,GAGd,IACT,CAGA,YAAYiD,EAAS,CAEnB,MAAME,EADS,KAAK,kBAAiB,EACP,OAAOhF,GAASA,EAAM,KAAO8E,CAAO,EAClE,YAAK,mBAAmBE,CAAc,EAE/B,EACT,CAGA,MAAM,eAAgB,CACpB,GAAI,CAEF,MAAMC,GAAW,MAAKC,EAAA,wBAAAC,CAAA,OAAC,QAAO,2BAAS,OAAAC,KAAA,kBAAAD,CAAA,8BAAG,QACpC,CAAE,OAAAE,CAAM,EAAK,MAAKH,EAAA,uBAAAG,CAAA,OAAC,QAAO,6BAAY,OAAAD,KAAA,iBAAAC,CAAA,6BAEtCxF,EAAS,KAAK,kBAAiB,EAmB/ByF,GAhBqB,IAAM,CAC/B,MAAMC,EAAc,KAAK,MAAM,aAAa,QAAQ,aAAa,GAAK,IAAI,EACpEC,EAASD,EAAY,IAAMA,EAAY,OAAS,UAEhDE,EAAe,aAAa,QAAQ,gBAAgBD,CAAM,EAAE,GAAK,UACjEE,EAAiB,aAAa,QAAQ,kBAAkBF,CAAM,EAAE,GAAK,UAE3E,MAAO,CACL,QAASC,EAAa,QAAQ,IAAK,EAAE,EACrC,UAAWC,EAAe,QAAQ,IAAK,EAAE,EACzC,OAAQ,SACR,KAAM,SACN,UAAW,QACrB,CACM,GAEiC,EAG3BvE,EAAW,IAAI8D,EAAQ,SAC7B9D,EAAS,QAAU,qBACnBA,EAAS,eAAiB,qBAC1BA,EAAS,QAAU,IAAI,KACvBA,EAAS,SAAW,IAAI,KAExB,MAAMG,EAAYH,EAAS,aAAa,sBAAsB,EAGxDwE,EAAU,CACd,UACA,QACA,UACA,QACA,QACA,QACA,SACA,gBACA,gBACA,UACR,EAEwBrE,EAAU,OAAOqE,CAAO,EAGhC,SAAUC,GAAS,CAC3BA,EAAK,KAAO,CAAE,KAAM,GAAM,MAAO,CAAE,KAAM,QAAQ,CAAE,EACnDA,EAAK,KAAO,CAAE,KAAM,UAAW,QAAS,QAAS,QAAS,CAAE,KAAMN,EAAO,OAAO,CAAE,EAClFM,EAAK,UAAY,CAAE,WAAY,SAAU,SAAU,QAAQ,EAC3DA,EAAK,OAAS,CACZ,IAAK,CAAE,MAAO,MAAM,EACpB,KAAM,CAAE,MAAO,MAAM,EACrB,OAAQ,CAAE,MAAO,MAAM,EACvB,MAAO,CAAE,MAAO,MAAM,CAChC,CACM,CAAC,EAGD/F,EAAO,QAAQG,GAAS,CACtB,MAAM4B,EAAMN,EAAU,OAAO,CAC3BtB,EAAM,MAAQ,IACdA,EAAM,MAAQ,IACdA,EAAM,OAAS,IACfA,EAAM,aAAe,IACrB,KAAK,mBAAmBA,EAAM,IAAI,EAClC,KAAK,uBAAuBA,EAAM,QAAQ,EAC1C,KAAK,qBAAqBA,EAAM,MAAM,EACtCA,EAAM,WAAa,IACnBA,EAAM,SAAWA,EAAM,SAAW,IAClCA,EAAM,SAAWA,EAAM,SAAS,KAAO,GACjD,CAAS,EAGD4B,EAAI,SAAUgE,GAAS,CACrBA,EAAK,OAAS,CACZ,IAAK,CAAE,MAAO,MAAM,EACpB,KAAM,CAAE,MAAO,MAAM,EACrB,OAAQ,CAAE,MAAO,MAAM,EACvB,MAAO,CAAE,MAAO,MAAM,CAClC,EACUA,EAAK,UAAY,CAAE,WAAY,SAAU,SAAU,QAAQ,CAC7D,CAAC,EAGG5F,EAAM,SAAW,YACnB4B,EAAI,SAAUgE,GAAS,CACrBA,EAAK,KAAO,CAAE,KAAM,UAAW,QAAS,QAAS,QAAS,CAAE,KAAM,QAAQ,CAAE,CAC9E,CAAC,EACQ5F,EAAM,SAAW,UAC1B4B,EAAI,SAAUgE,GAAS,CACrBA,EAAK,KAAO,CAAE,KAAM,UAAW,QAAS,QAAS,QAAS,CAAE,KAAM,QAAQ,CAAE,CAC9E,CAAC,EACQ5F,EAAM,SAAW,UAC1B4B,EAAI,SAAUgE,GAAS,CACrBA,EAAK,KAAO,CAAE,KAAM,UAAW,QAAS,QAAS,QAAS,CAAE,KAAMN,EAAO,SAAS,CAAE,CACtF,CAAC,CAEL,CAAC,EAGDhE,EAAU,QAAU,CAClB,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,EACX,CAAE,MAAO,EAAE,CACnB,EAGM,MAAMuE,EAAS,MAAM1E,EAAS,KAAK,YAAW,EACxC2E,EAAO,IAAI,KAAK,CAACD,CAAM,EAAG,CAAE,KAAM,mEAAmE,CAAE,EACvGE,EAAW,YAAY,IAAI,KAAI,EAAG,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC,QAEnEV,EAAOS,EAAMC,CAAQ,EAErB,QAAQ,IAAI,wCAAwC,CAEtD,OAAS3F,EAAO,CACd,QAAQ,MAAM,8BAA+BA,CAAK,EAKlD,MAAM4F,EAFS,KAAK,kBAAiB,EAEX,IAAIhG,IAAU,CACtC,QAAWA,EAAM,KACjB,MAASA,EAAM,KACf,QAAWA,EAAM,MACjB,MAASA,EAAM,YACf,MAAS,KAAK,mBAAmBA,EAAM,IAAI,EAC3C,MAAS,KAAK,uBAAuBA,EAAM,QAAQ,EACnD,OAAU,KAAK,qBAAqBA,EAAM,MAAM,EAChD,gBAAiBA,EAAM,WAAa,IACpC,gBAAiBA,EAAM,SAAWA,EAAM,SAAW,IACnD,SAAYA,EAAM,SAAWA,EAAM,SAAS,KAAO,GAC3D,EAAQ,EAEIiG,EAAKzE,EAAW,cAAcwE,CAAU,EACxCE,EAAK1E,EAAW,SAAQ,EAC9BA,EAAW,kBAAkB0E,EAAID,EAAI,UAAU,EAE/C,MAAMF,EAAW,YAAY,IAAI,KAAI,EAAG,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC,QACnEI,EAAeD,EAAIH,CAAQ,CAC7B,CACF,CAGA,mBAAmBnG,EAAM,CAQvB,MAPc,CACZ,eAAkB,eAClB,aAAgB,eAChB,SAAY,WACZ,SAAY,OACZ,KAAQ,MACd,EACiBA,CAAI,GAAKA,CACxB,CAGA,uBAAuBwG,EAAU,CAO/B,MANmB,CACjB,SAAY,SACZ,SAAY,WACZ,SAAY,OACZ,KAAQ,MACd,EACsBA,CAAQ,GAAKA,CACjC,CAGA,qBAAqBzB,EAAQ,CAU3B,MATiB,CACf,UAAa,QACb,YAAe,cACf,UAAa,QACb,UAAa,OACb,QAAW,QACX,OAAU,OACV,QAAW,aACjB,EACoBA,CAAM,GAAKA,CAC7B,CAGA,0BAA2B,CACzB,QAAQ,IAAI,sDAAsD,EAElE,GAAI,CAEF,YAAK,cAAa,EAClB,KAAK,kBAAiB,EACtB,KAAK,gBAAe,EACpB,KAAK,kBAAiB,EAEtB,QAAQ,IAAI,wDAAwD,EAC7D,EACT,OAASvE,EAAO,CACd,eAAQ,MAAM,uDAAwDA,CAAK,EACpE,EACT,CACF,CAGA,aAAc,CACZ,QAAQ,IAAI,wCAAwC,EAEpD,GAAI,CAEF,YAAK,cAAa,EAClB,KAAK,kBAAiB,EACtB,KAAK,gBAAe,EACpB,KAAK,kBAAiB,EAGtB,KAAK,sBAAqB,EAE1B,QAAQ,IAAI,8BAA8B,EACnC,EACT,OAASA,EAAO,CACd,eAAQ,MAAM,6BAA8BA,CAAK,EAC1C,EACT,CACF,CAGA,uBAAwB,CACtB,QAAQ,IAAI,gDAAgD,EAE5D,MAAMP,EAAS,KAAK,kBAAiB,EAGrC,KAAK,8BAA8BA,CAAM,EAGzC,KAAK,0BAA0BA,CAAM,EAGrC,KAAK,4BAA4BA,CAAM,CACzC,CAGA,8BAA8BA,EAAQ,CACpC,GAAI,CACF,MAAMyE,EAAY,KAAK,MAAM,aAAa,QAAQ,gBAAgB,GAAK,IAAI,EACrE+B,EAAoBxG,EAAO,OAAOG,GAASA,EAAM,OAAS,YAAc,CAACA,EAAM,QAAQ,EAE7F,QAAQ,IAAI,sCAAuCqG,EAAkB,MAAM,EAE3EA,EAAkB,QAAQrG,GAAS,CAIjC,GAAI,CAFqBsE,EAAU,KAAKgC,GAAKA,EAAE,QAAUtG,EAAM,OAASsG,EAAE,YAActG,EAAM,IAAI,EAE3E,CACrB,MAAMuG,EAAc,CAClB,GAAI,YAAY,KAAK,IAAG,CAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GACrE,MAAOvG,EAAM,MACb,YAAaA,EAAM,aAAe,kCAClC,UAAWA,EAAM,KACjB,UAAWA,EAAM,MAAQ,QACzB,SAAUA,EAAM,UAAY,IAC5B,OAAQ,YACR,QAAS,wBACT,iBAAkB,GAClB,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,OAAQ,UACpB,EAEUsE,EAAU,KAAKiC,CAAW,EAC1B,QAAQ,IAAI,6BAA8BA,EAAY,KAAK,EAG3DvG,EAAM,SAAWuG,EAAY,EAC/B,CACF,CAAC,EAED,aAAa,QAAQ,iBAAkB,KAAK,UAAUjC,CAAS,CAAC,EAChE,KAAK,mBAAmBzE,CAAM,CAEhC,OAASO,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,CACnD,CACF,CAGA,0BAA0BP,EAAQ,CAChC,GAAI,CACF,MAAM2E,EAAQ,KAAK,MAAM,aAAa,QAAQ,YAAY,GAAK,IAAI,EAC7DgC,EAAgB3G,EAAO,OAAOG,GAASA,EAAM,OAAS,QAAU,CAACA,EAAM,QAAQ,EAErF,QAAQ,IAAI,mCAAoCwG,EAAc,MAAM,EAEpEA,EAAc,QAAQxG,GAAS,CAI7B,GAAI,CAFiBwE,EAAM,KAAKiC,GAAKA,EAAE,QAAUzG,EAAM,OAASyG,EAAE,UAAYzG,EAAM,IAAI,EAErE,CACjB,MAAM0G,EAAU,CACd,GAAI,QAAQ,KAAK,IAAG,CAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GACjE,MAAO1G,EAAM,MACb,YAAaA,EAAM,aAAe,8BAClC,QAASA,EAAM,KACf,QAASA,EAAM,MAAQ,QACvB,OAAQ,YACR,OAAQ,wBACR,QAAS,wBACT,cAAe,GACf,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,OAAQ,UACpB,EAEUwE,EAAM,KAAKkC,CAAO,EAClB,QAAQ,IAAI,yBAA0BA,EAAQ,KAAK,EAGnD1G,EAAM,SAAW0G,EAAQ,EAC3B,CACF,CAAC,EAED,aAAa,QAAQ,aAAc,KAAK,UAAUlC,CAAK,CAAC,EACxD,KAAK,mBAAmB3E,CAAM,CAEhC,OAASO,EAAO,CACd,QAAQ,MAAM,0BAA2BA,CAAK,CAChD,CACF,CAGA,4BAA4BP,EAAQ,CAClC,GAAI,CACF,MAAM8G,EAAU,KAAK,MAAM,aAAa,QAAQ,cAAc,GAAK,IAAI,EACjEC,EAAkB/G,EAAO,OAAOG,GAASA,EAAM,OAAS,UAAY,CAACA,EAAM,QAAQ,EAEzF,QAAQ,IAAI,oCAAqC4G,EAAgB,MAAM,EAEvEA,EAAgB,QAAQ5G,GAAS,CAI/B,GAAI,CAFmB2G,EAAQ,KAAKE,GAAKA,EAAE,QAAU7G,EAAM,OAAS6G,EAAE,YAAc7G,EAAM,IAAI,EAEzE,CACnB,MAAM8G,EAAY,CAChB,GAAI,UAAU,KAAK,IAAG,CAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,GACnE,MAAO9G,EAAM,MACb,YAAaA,EAAM,aAAe,8BAClC,UAAWA,EAAM,KACjB,UAAWA,EAAM,MAAQ,QACzB,SAAUA,EAAM,UAAY,IAC5B,OAAQ,YACR,WAAY,wBACZ,iBAAkB,GAClB,UAAW,IAAI,KAAI,EAAG,YAAW,EACjC,OAAQ,UACpB,EAEU2G,EAAQ,KAAKG,CAAS,EACtB,QAAQ,IAAI,yBAA0BA,EAAU,KAAK,EAGrD9G,EAAM,SAAW8G,EAAU,EAC7B,CACF,CAAC,EAED,aAAa,QAAQ,eAAgB,KAAK,UAAUH,CAAO,CAAC,EAC5D,KAAK,mBAAmB9G,CAAM,CAEhC,OAASO,EAAO,CACd,QAAQ,MAAM,2BAA4BA,CAAK,CACjD,CACF,CAGA,eAAgB,CACd,GAAI,CAEF,MAAMoE,EAAQ,KAAK,MAAM,aAAa,QAAQ,WAAW,GAAK,IAAI,EAC5D3E,EAAS,KAAK,kBAAiB,EAC/BU,EAAY,KAAK,MAAM,aAAa,QAAQ,gBAAgB,GAAK,IAAI,EAE3E,QAAQ,IAAI,oBAAqBiE,EAAM,OAAQ,MAAM,EAErDA,EAAM,QAAQC,GAAQ,CAEpB,GAAI,CAEF,GADkB,KAAK,MAAM,aAAa,QAAQ,gBAAgB,GAAK,IAAI,EAC7D,KAAKsC,GAAoBtC,EAAK,OAASA,EAAK,MAAM,SAASsC,CAAgB,CAAC,EAAG,CAC3F,QAAQ,IAAI,oCAAqCtC,EAAK,KAAK,EAC3D,MACF,CACF,OAASrE,EAAO,CACd,QAAQ,MAAM,8BAA+BA,CAAK,CACpD,CAKA,MAAM4G,EAAqBnH,EAAO,KAAKG,GACrCA,EAAM,OAAS,cAAgBA,EAAM,WAAayE,EAAK,EACjE,EACcwC,EAAmBpH,EAAO,KAAKG,GACnCA,EAAM,OAAS,YAAcA,EAAM,WAAayE,EAAK,EAC/D,EAGQ,GAAI,CAACuC,GAAsBvC,EAAK,UAAW,CACzC,MAAMyC,EAAa,KAAK,wBAAwBzC,EAAK,UAAWlE,CAAS,EACzE,QAAQ,IAAI,oCAAqCkE,EAAK,KAAK,EAC3D,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,QAASA,EAAK,KAAK,EAChD,YAAaA,EAAK,aAAe,KAAK,uBAAuB,WAAW,EACxE,KAAM,aACN,KAAMA,EAAK,UACX,KAAMA,EAAK,WAAa,QACxB,SAAU,OACV,OAAQA,EAAK,SAAW,YAAc,YAAc,QACpD,SAAUA,EAAK,GACf,KAAM,KACN,MAAOyC,EACP,OAAQzC,EAAK,OACb,QAASA,EAAK,QACd,cAAeA,EAAK,cAAgBA,EAAK,cAAc,OAAS,CAC5E,CAAW,CACH,CAGA,GAAI,CAACwC,GAAoBxC,EAAK,QAAS,CACrC,MAAM0C,EAAW,KAAK,wBAAwB1C,EAAK,QAASlE,CAAS,EACrE,QAAQ,IAAI,yCAA0CkE,EAAK,KAAK,EAChE,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,MAAOA,EAAK,KAAK,EAC9C,YAAaA,EAAK,aAAe,KAAK,uBAAuB,SAAS,EACtE,KAAM,WACN,KAAMA,EAAK,QACX,KAAMA,EAAK,SAAW,QACtB,SAAU,OACV,OAAQA,EAAK,SAAW,YAAc,YAAc,QACpD,SAAUA,EAAK,GACf,KAAM,KACN,MAAO0C,EACP,OAAQ1C,EAAK,OACb,QAASA,EAAK,QACd,cAAeA,EAAK,cAAgBA,EAAK,cAAc,OAAS,CAC5E,CAAW,CACH,CACF,CAAC,CACH,OAASrE,EAAO,CACd,QAAQ,MAAM,wBAAyBA,CAAK,CAC9C,CACF,CAGA,mBAAoB,CAClB,GAAI,CAEF,MAAMgH,EAAuB,KAAK,MAAM,aAAa,QAAQ,eAAe,GAAK,IAAI,EAGrF,IAAIC,EAA2B,GAC/B,GAAI,CACF,KAAM,CAAE,QAASC,CAAW,EAAK,QAAQ,sBAAsB,EAC/DD,EAA2BC,EAAY,iBAAmB,EAC5D,OAASlH,EAAO,CACd,QAAQ,IAAI,mCAAoCA,EAAM,OAAO,CAC/D,CAMA,MAAMmH,EAHe,CAAC,GAAGH,EAAsB,GAAGC,CAAwB,EAGrC,OAAO,CAACG,EAAKC,KACvBD,EAAI,KAAKpF,GAAYA,EAAS,KAAOqF,EAAQ,EAAE,GAEtED,EAAI,KAAKC,CAAO,EAEXD,GACN,EAAE,EAEC3H,EAAS,KAAK,kBAAiB,EAC/BU,EAAY,KAAK,MAAM,aAAa,QAAQ,gBAAgB,GAAK,IAAI,EAE3E,QAAQ,IAAI,uBAAwBgH,EAAgB,OAAQ,MAAM,EAElEA,EAAgB,QAAQnF,GAAY,CAElC,MAAM4E,EAAqBnH,EAAO,KAAKG,GACrCA,EAAM,OAAS,kBAAoBA,EAAM,WAAaoC,EAAS,EACzE,EACcsF,EAAmB7H,EAAO,KAAKG,GACnCA,EAAM,OAAS,gBAAkBA,EAAM,WAAaoC,EAAS,EACvE,EAGQ,GAAI,CAAC4E,GAAsB5E,EAAS,UAAW,CAC7C,MAAM8E,EAAa,KAAK,wBAAwB9E,EAAS,UAAW7B,CAAS,EAC7E,QAAQ,IAAI,wCAAyC6B,EAAS,KAAK,EACnE,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,QAASA,EAAS,KAAK,EACpD,YAAaA,EAAS,aAAe,KAAK,uBAAuB,eAAe,EAChF,KAAM,iBACN,KAAMA,EAAS,UACf,KAAMA,EAAS,WAAa,QAC5B,SAAU,WACV,OAAQA,EAAS,SAAW,YAAc,YAAc,QACxD,SAAUA,EAAS,GACnB,KAAM,KACN,MAAO8E,EACP,QAAS9E,EAAS,QAClB,iBAAkBA,EAAS,iBAAmBA,EAAS,iBAAiB,OAAS,CAC7F,CAAW,CACH,CAGA,GAAI,CAACsF,GAAoBtF,EAAS,QAAS,CACzC,MAAMuF,EAAW,KAAK,wBAAwBvF,EAAS,QAAS7B,CAAS,EACzE,QAAQ,IAAI,wCAAyC6B,EAAS,KAAK,EACnE,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,MAAOA,EAAS,KAAK,EAClD,YAAaA,EAAS,aAAe,KAAK,uBAAuB,aAAa,EAC9E,KAAM,eACN,KAAMA,EAAS,QACf,KAAMA,EAAS,SAAW,QAC1B,SAAU,WACV,OAAQA,EAAS,SAAW,YAAc,YAAc,QACxD,SAAUA,EAAS,GACnB,KAAM,KACN,MAAOuF,EACP,QAASvF,EAAS,QAClB,iBAAkBA,EAAS,iBAAmBA,EAAS,iBAAiB,OAAS,CAC7F,CAAW,CACH,CACF,CAAC,CACH,OAAShC,EAAO,CACd,QAAQ,MAAM,2BAA4BA,CAAK,CACjD,CACF,CAGA,iBAAkB,CAChB,GAAI,CAEF,MAAMuG,EAAU,KAAK,MAAM,aAAa,QAAQ,aAAa,GAAK,IAAI,EAChE9G,EAAS,KAAK,kBAAiB,EAC/BU,EAAY,KAAK,MAAM,aAAa,QAAQ,gBAAgB,GAAK,IAAI,EAE3E,QAAQ,IAAI,iCAAkCoG,EAAQ,MAAM,EAC5D,QAAQ,IAAI,qBAAsBA,EAAQ,IAAIE,IAAM,CAAE,GAAIA,EAAE,GAAI,MAAOA,EAAE,YAAcA,EAAE,MAAO,UAAWA,EAAE,UAAW,QAASA,EAAE,OAAO,EAAG,CAAC,EAE9I,QAAQ,IAAI,qBAAsBF,EAAQ,OAAQ,MAAM,EAExDA,EAAQ,QAAQiB,GAAU,CAExB,MAAMZ,EAAqBnH,EAAO,KAAKG,GACrCA,EAAM,OAAS,gBAAkBA,EAAM,WAAa4H,EAAO,EACrE,EACcF,EAAmB7H,EAAO,KAAKG,GACnCA,EAAM,OAAS,cAAgBA,EAAM,WAAa4H,EAAO,EACnE,EAGQ,GAAI,CAACZ,GAAsBY,EAAO,UAAW,CAC3C,MAAMV,EAAa,KAAK,wBAAwBU,EAAO,UAAWrH,CAAS,EAC3E,QAAQ,IAAI,oCAAqCqH,EAAO,KAAK,EAC7D,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,QAASA,EAAO,YAAcA,EAAO,KAAK,EACvE,YAAaA,EAAO,aAAe,KAAK,uBAAuB,aAAa,EAC5E,KAAM,eACN,KAAMA,EAAO,UACb,KAAMA,EAAO,WAAa,QAC1B,SAAU,SACV,OAAQA,EAAO,SAAW,YAAc,YAAc,QACtD,SAAUA,EAAO,GACjB,KAAM,KACN,MAAOV,EACP,WAAYU,EAAO,SAAWA,EAAO,WACrC,cAAeA,EAAO,iBAAmBA,EAAO,iBAAiB,OAAS,CACtF,CAAW,CACH,CAGA,GAAI,CAACF,GAAoBE,EAAO,QAAS,CACvC,MAAMD,EAAW,KAAK,wBAAwBC,EAAO,QAASrH,CAAS,EACvE,QAAQ,IAAI,oCAAqCqH,EAAO,KAAK,EAC7D,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,MAAOA,EAAO,YAAcA,EAAO,KAAK,EACrE,YAAaA,EAAO,aAAe,KAAK,uBAAuB,WAAW,EAC1E,KAAM,aACN,KAAMA,EAAO,QACb,KAAMA,EAAO,SAAW,QACxB,SAAU,SACV,OAAQA,EAAO,SAAW,YAAc,YAAc,QACtD,SAAUA,EAAO,GACjB,KAAM,KACN,MAAOD,EACP,WAAYC,EAAO,SAAWA,EAAO,WACrC,cAAeA,EAAO,iBAAmBA,EAAO,iBAAiB,OAAS,CACtF,CAAW,CACH,CACF,CAAC,CACH,OAASxH,EAAO,CACd,QAAQ,MAAM,yBAA0BA,CAAK,CAC/C,CACF,CAGA,mBAAoB,CAClB,MAAMG,EAAY,KAAK,MAAM,aAAa,QAAQ,eAAe,GAAK,IAAI,EACpEV,EAAS,KAAK,kBAAiB,EAErCU,EAAU,QAAQG,GAAY,CAEDb,EAAO,KAAKG,GACrCA,EAAM,OAAS,kBAAoBA,EAAM,WAAaU,EAAS,EACvE,GAGQ,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,QAASA,EAAS,IAAI,EACnD,YAAaA,EAAS,aAAe,KAAK,uBAAuB,eAAe,EAChF,KAAM,iBACN,KAAMA,EAAS,UACf,KAAMA,EAAS,WAAa,QAC5B,SAAU,WACV,OAAQ,YACR,SAAUA,EAAS,GACnB,KAAM,KACN,MAAO,SACP,SAAUA,CACpB,CAAS,EAQC,CAJqBb,EAAO,KAAKG,GACnCA,EAAM,OAAS,gBAAkBA,EAAM,WAAaU,EAAS,EACrE,GAE+BA,EAAS,SAChC,KAAK,SAAS,CACZ,MAAO,KAAK,iBAAiB,MAAOA,EAAS,IAAI,EACjD,YAAa,KAAK,uBAAuB,aAAa,EACtD,KAAM,eACN,KAAMA,EAAS,QACf,KAAMA,EAAS,SAAW,QAC1B,SAAU,WACV,OAAQ,YACR,SAAUA,EAAS,GACnB,KAAM,IACN,MAAO,QACP,SAAUA,CACpB,CAAS,CAEL,CAAC,CACH,CAKA,sBAAsBmH,EAAUC,EAAW,CACzC,GAAI,CAEF,MAAM/H,EADS,KAAK,kBAAiB,EACR,OAAOC,GAClC,EAAEA,EAAM,WAAa6H,GAAY7H,EAAM,OAAS8H,EACxD,EACM,YAAK,mBAAmB/H,CAAa,EACrC,QAAQ,IAAI,kCAAkC8H,CAAQ,EAAE,EACjD,EACT,OAASzH,EAAO,CACd,eAAQ,MAAM,mCAAoCA,CAAK,EAChD,EACT,CACF,CAGA,iBAAiB2H,EAAQ,CACvB,GAAI,CAEF,MAAMhI,EADS,KAAK,kBAAiB,EACR,OAAOC,GAAS,OAC3C,MAAMgI,EAAchI,EAAM,WAAa,UAAUR,EAAAQ,EAAM,OAAN,YAAAR,EAAY,SAAS,SAChEyI,EAAgBjI,EAAM,WAAa+H,GACpB/H,EAAM,WAAa,GAAG+H,CAAM,UAC5B/H,EAAM,WAAa,GAAG+H,CAAM,QAC5B/H,EAAM,WAAa+H,EAAO,SAAQ,EAEvD,MAAO,EAAEC,GAAeC,EAC1B,CAAC,EACD,YAAK,mBAAmBlI,CAAa,EACrC,QAAQ,IAAI,8CAA8CgI,CAAM,EAAE,EAC3D,EACT,OAAS3H,EAAO,CACd,eAAQ,MAAM,0CAA2CA,CAAK,EACvD,EACT,CACF,CAGA,qBAAqB8H,EAAY,CAC/B,GAAI,CACF,QAAQ,IAAI,+CAAgDA,CAAU,EAEtE,MAAMrI,EAAS,KAAK,kBAAiB,EACrC,QAAQ,IAAI,+BAAgCA,EAAO,MAAM,EAGzD,MAAMsI,EAAiBtI,EAAO,OAAOG,GAAS,SAC5C,MAAMoI,EAAkBpI,EAAM,WAAa,cAAcR,EAAAQ,EAAM,OAAN,YAAAR,EAAY,SAAS,aACxE6I,EAAoBrI,EAAM,WAAakI,GACpBlI,EAAM,WAAa,GAAGkI,CAAU,UAChClI,EAAM,WAAa,GAAGkI,CAAU,QAChClI,EAAM,WAAakI,EAAW,SAAQ,GACtClI,EAAM,WAAa,SAASkI,CAAU,KACtCI,EAAAtI,EAAM,QAAN,YAAAsI,EAAa,SAASJ,IAE/C,OAAOE,GAAmBC,CAC5B,CAAC,EAED,QAAQ,IAAI,mCAAoC,CAC9C,WAAAH,EACA,YAAaC,EAAe,OAC5B,YAAaA,EAAe,IAAIlH,GAAKA,EAAE,KAAK,CACpD,CAAO,EAED,MAAMlB,EAAgBF,EAAO,OAAOG,GAAS,SAC3C,MAAMoI,EAAkBpI,EAAM,WAAa,cAAcR,EAAAQ,EAAM,OAAN,YAAAR,EAAY,SAAS,aACxE6I,EAAoBrI,EAAM,WAAakI,GACpBlI,EAAM,WAAa,GAAGkI,CAAU,UAChClI,EAAM,WAAa,GAAGkI,CAAU,QAChClI,EAAM,WAAakI,EAAW,SAAQ,GACtClI,EAAM,WAAa,SAASkI,CAAU,KACtCI,EAAAtI,EAAM,QAAN,YAAAsI,EAAa,SAASJ,IAE/C,MAAO,EAAEE,GAAmBC,EAC9B,CAAC,EAED,eAAQ,IAAI,+BAAgCtI,EAAc,MAAM,EAChE,QAAQ,IAAI,WAAYF,EAAO,OAASE,EAAc,OAAQ,oBAAoB,EAElF,KAAK,mBAAmBA,CAAa,EACrC,QAAQ,IAAI,kDAAkDmI,CAAU,EAAE,EACnE,EACT,OAAS9H,EAAO,CACd,eAAQ,MAAM,gDAAiDA,CAAK,EAC7D,EACT,CACF,CAGA,mBAAmB0H,EAAW,CAC5B,GAAI,CAEF,MAAM/H,EADS,KAAK,kBAAiB,EACR,OAAOC,GAASA,EAAM,OAAS8H,CAAS,EACrE,YAAK,mBAAmB/H,CAAa,EACrC,QAAQ,IAAI,uBAAuB+H,CAAS,iBAAiB,EACtD,EACT,OAAS1H,EAAO,CACd,eAAQ,MAAM,oBAAoB0H,CAAS,mBAAoB1H,CAAK,EAC7D,EACT,CACF,CAGA,gBAAiB,CACf,GAAI,CACF,oBAAa,WAAW,KAAK,UAAU,EACvC,aAAa,WAAW,KAAK,YAAY,EACzC,aAAa,QAAQ,KAAK,WAAY,KAAK,UAAU,EAAE,CAAC,EACxD,QAAQ,IAAI,iCAAiC,EACtC,EACT,OAASA,EAAO,CACd,eAAQ,MAAM,0BAA2BA,CAAK,EACvC,EACT,CACF,CAGA,eAAgB,CACd,QAAQ,IAAI,+BAA+B,EAC3C,aAAa,WAAW,KAAK,UAAU,EACvC,aAAa,WAAW,KAAK,YAAY,EACzC,QAAQ,IAAI,8BAA8B,CAC5C,CACF,CAEY,MAACmI,EAAkB,IAAInJ", "names": ["TimelineService", "key", "language", "currentLanguage", "_a", "timelineTranslations", "prefix", "title", "type", "events", "has<PERSON><PERSON><PERSON>", "updatedEvents", "event", "newTitle", "newDescription", "title<PERSON>art", "error", "updated", "eventDate", "rotations", "eventDateObj", "i", "rotation", "startDate", "endDate", "file", "resolve", "reject", "reader", "e", "data", "workbook", "XLSX.read", "sheetName", "worksheet", "jsonData", "XLSX.utils", "currentRotation", "rotationCounter", "columns", "row", "index", "rotationValue", "workshopValue", "activityValue", "dateValue", "timeValue", "rotationNumber", "workshop", "a", "b", "rotationText", "patterns", "pattern", "match", "number", "dateStr", "excelEpoch", "result", "dateFormats", "format", "day", "month", "year", "monthNum", "date", "timeStr", "timePatterns", "hours", "minutes", "period", "startHour", "startMin", "endHour", "endMin", "startMinutes", "monthName", "months", "<PERSON><PERSON><PERSON>", "cleanEvents", "cleanEvent", "stored", "workshops", "workshopEvent", "tasks", "task", "taskEvent", "status", "existingEvent", "newEvent", "eventId", "updates", "filteredEvents", "ExcelJS", "__vitePreload", "__vite_default__", "n", "saveAs", "colors", "currentUser", "userId", "primaryColor", "secondaryColor", "headers", "cell", "buffer", "blob", "fileName", "exportData", "ws", "wb", "XLSX.writeFile", "category", "timelineWorkshops", "w", "newWorkshop", "timelineTasks", "t", "newTask", "courses", "timelineCourses", "c", "newCourse", "blacklisted<PERSON><PERSON><PERSON>", "existingStartEvent", "existingDueEvent", "startColor", "dueColor", "workshopsFromStorage", "workshopsFromDataStorage", "dataStorage", "uniqueWorkshops", "acc", "current", "existingEndEvent", "endColor", "course", "sourceId", "eventType", "taskId", "isTaskEvent", "matchesTaskId", "workshopId", "workshopEvents", "isWorkshopEvent", "matchesWorkshopId", "_b", "timelineService"], "ignoreList": [], "sources": ["../../src/services/timelineService.js"], "sourcesContent": ["import * as XLSX from 'xlsx'\nimport { timelineTranslations } from '../translations/timeline.js'\n\nclass TimelineService {\n  constructor() {\n    this.storageKey = 'app_timeline_events'\n    this.rotationsKey = 'app_rotations'\n\n    // ألوان الروتيشن\n    this.rotationColors = [\n      'blue',    // الروتيشن الأول\n      'green',   // الروتيشن الثاني\n      'purple',  // الروتيشن الثالث\n      'orange',  // الروتيشن الرابع\n      'red',     // الروتيشن الخامس\n      'indigo',  // الروتيشن السادس\n      'pink',    // الروتيشن السابع\n      'teal'     // الروتيشن الثامن\n    ]\n  }\n\n  // دالة مساعدة للحصول على الترجمة المناسبة\n  getTranslation(key, language = 'ar') {\n    const currentLanguage = language || localStorage.getItem('language') || 'ar'\n    return timelineTranslations[currentLanguage]?.[key] || timelineTranslations.ar[key] || key\n  }\n\n  // دالة لإنشاء عنوان الحدث مع الترجمة المناسبة\n  createEventTitle(prefix, title, language = null) {\n    const currentLanguage = language || localStorage.getItem('language') || 'ar'\n    const translatedPrefix = this.getTranslation(`timeline.eventPrefix.${prefix}`, currentLanguage)\n    return `${translatedPrefix} ${title}`\n  }\n\n  // دالة لإنشاء وصف الحدث مع الترجمة المناسبة\n  createEventDescription(type, language = null) {\n    const currentLanguage = language || localStorage.getItem('language') || 'ar'\n    return this.getTranslation(`timeline.eventDesc.${type}`, currentLanguage)\n  }\n\n  // دالة لتحديث عناوين الأحداث الموجودة لتستخدم الترجمات\n  updateExistingEventTitles() {\n    try {\n      const events = this.getTimelineEvents()\n      let hasChanges = false\n\n      const updatedEvents = events.map(event => {\n        let newTitle = event.title\n        let newDescription = event.description\n\n        // تحديث العناوين التي تحتوي على النصوص العربية المباشرة\n        if (event.title.includes('بداية:')) {\n          const titlePart = event.title.replace('بداية:', '').trim()\n          newTitle = this.createEventTitle('start', titlePart)\n          hasChanges = true\n        } else if (event.title.includes('نهاية:')) {\n          const titlePart = event.title.replace('نهاية:', '').trim()\n          newTitle = this.createEventTitle('end', titlePart)\n          hasChanges = true\n        } else if (event.title.includes('تسليم:') || event.title.includes('موعد تسليم:') || event.title.includes('موعد التسليم:')) {\n          const titlePart = event.title.replace(/تسليم:|موعد تسليم:|موعد التسليم:/, '').trim()\n          newTitle = this.createEventTitle('due', titlePart)\n          hasChanges = true\n        } else if (event.title.includes('📝 بداية:')) {\n          const titlePart = event.title.replace('📝 بداية:', '').trim()\n          newTitle = `📝 ${this.createEventTitle('start', titlePart)}`\n          hasChanges = true\n        } else if (event.title.includes('⏰ موعد تسليم:')) {\n          const titlePart = event.title.replace('⏰ موعد تسليم:', '').trim()\n          newTitle = `⏰ ${this.createEventTitle('due', titlePart)}`\n          hasChanges = true\n        }\n\n        // تحديث الأوصاف\n        if (event.description === 'بداية مهمة تدريبية') {\n          newDescription = this.createEventDescription('taskStart')\n          hasChanges = true\n        } else if (event.description === 'نهاية مهمة تدريبية' || event.description === 'موعد تسليم مهمة تدريبية') {\n          newDescription = this.createEventDescription('taskDue')\n          hasChanges = true\n        } else if (event.description === 'بداية ورشة عمل تدريبية') {\n          newDescription = this.createEventDescription('workshopStart')\n          hasChanges = true\n        } else if (event.description === 'نهاية ورشة عمل تدريبية') {\n          newDescription = this.createEventDescription('workshopEnd')\n          hasChanges = true\n        } else if (event.description === 'بداية دورة تدريبية') {\n          newDescription = this.createEventDescription('courseStart')\n          hasChanges = true\n        } else if (event.description === 'نهاية دورة تدريبية') {\n          newDescription = this.createEventDescription('courseEnd')\n          hasChanges = true\n        } else if (event.description === 'بداية روتيشن تدريبي' || event.description === 'بداية الروتيشن التدريبي') {\n          newDescription = this.createEventDescription('rotationStart')\n          hasChanges = true\n        } else if (event.description === 'نهاية الروتيشن التدريبي') {\n          newDescription = this.createEventDescription('rotationEnd')\n          hasChanges = true\n        }\n\n        return {\n          ...event,\n          title: newTitle,\n          description: newDescription\n        }\n      })\n\n      if (hasChanges) {\n        this.saveTimelineEvents(updatedEvents)\n        console.log('✅ تم تحديث عناوين الأحداث لتستخدم الترجمات')\n        return true\n      }\n\n      return false\n    } catch (error) {\n      console.error('❌ خطأ في تحديث عناوين الأحداث:', error)\n      return false\n    }\n  }\n\n  // دالة لتشغيل التحديث فوراً\n  forceUpdateEventTitles() {\n    console.log('🔄 تشغيل تحديث فوري لعناوين الأحداث...')\n    const updated = this.updateExistingEventTitles()\n    if (updated) {\n      // إشعار جميع المكونات بالتحديث\n      window.dispatchEvent(new Event('timelineUpdated'))\n      window.dispatchEvent(new Event('timelineTranslationsUpdated'))\n    }\n    return updated\n  }\n\n  // تحديد لون الحدث حسب الروتيشن\n  getEventColorByRotation(eventDate, rotations) {\n    try {\n      // العثور على الروتيشن الذي يقع فيه التاريخ\n      const eventDateObj = new Date(eventDate)\n\n      for (let i = 0; i < rotations.length; i++) {\n        const rotation = rotations[i]\n        const startDate = new Date(rotation.startDate)\n        const endDate = new Date(rotation.endDate)\n\n        if (eventDateObj >= startDate && eventDateObj <= endDate) {\n          // إرجاع لون الروتيشن\n          return this.rotationColors[i % this.rotationColors.length]\n        }\n      }\n\n      // إذا لم يكن الحدث ضمن أي روتيشن، استخدم اللون الافتراضي\n      return 'gray'\n    } catch (error) {\n      console.error('خطأ في تحديد لون الحدث:', error)\n      return 'gray'\n    }\n  }\n\n  // استيراد التايم لاين من ملف Excel\n  async importTimelineFromExcel(file) {\n    return new Promise((resolve, reject) => {\n      console.log('🔄 بدء استيراد ملف Excel:', file.name)\n      const reader = new FileReader()\n\n      reader.onload = (e) => {\n        try {\n          console.log('📖 قراءة محتوى الملف...')\n          const data = new Uint8Array(e.target.result)\n          const workbook = XLSX.read(data, { type: 'array' })\n\n          console.log('📊 أوراق العمل المتاحة:', workbook.SheetNames)\n          const sheetName = workbook.SheetNames[0]\n          const worksheet = workbook.Sheets[sheetName]\n          const jsonData = XLSX.utils.sheet_to_json(worksheet)\n\n          console.log('📋 البيانات المستخرجة:', jsonData.length, 'صف')\n          console.log('📋 عينة من البيانات:', jsonData.slice(0, 3))\n\n          const events = this.parseExcelData(jsonData)\n          console.log('✅ تم تحليل', events.length, 'حدث')\n\n          // لا نحفظ هنا، فقط نعيد البيانات للمعاينة\n          resolve(events)\n        } catch (error) {\n          console.error('❌ خطأ في استيراد Excel:', error)\n          reject(error)\n        }\n      }\n\n      reader.onerror = () => {\n        console.error('❌ فشل في قراءة الملف')\n        reject(new Error('فشل في قراءة الملف'))\n      }\n      reader.readAsArrayBuffer(file)\n    })\n  }\n\n  // تحليل بيانات Excel وتحويلها إلى أحداث\n  parseExcelData(data) {\n    console.log('🔍 بدء تحليل بيانات Excel...')\n    const events = []\n    let currentRotation = null\n    let rotationCounter = 1\n\n    if (!data || data.length === 0) {\n      console.warn('⚠️ لا توجد بيانات للتحليل')\n      return events\n    }\n\n    // طباعة أسماء الأعمدة المتاحة\n    const columns = Object.keys(data[0] || {})\n    console.log('📊 الأعمدة المتاحة:', columns)\n\n    data.forEach((row, index) => {\n      try {\n        console.log(`📝 تحليل الصف ${index + 1}:`, row)\n\n        // التحقق من أن الصف ليس فارغاً\n        if (!row || typeof row !== 'object') {\n          console.log('⚠️ صف فارغ أو غير صالح، تخطي...')\n          return\n        }\n\n      // البحث عن عمود الروتيشن بطرق مختلفة وتحويل إلى نص\n      const rotationValue = String(row.Rotation || row.rotation || row['Rotation'] ||\n                                  row['روتيشن'] || row['الروتيشن'] || '').trim()\n\n      // البحث عن عمود ورش العمل وتحويل إلى نص\n      const workshopValue = String(row.Workshop || row.workshop || row['Workshop'] ||\n                                  row['ورشة العمل'] || row['ورش العمل'] || '').trim()\n\n      // البحث عن عمود الأنشطة وتحويل إلى نص\n      const activityValue = String(row['Other Activities'] || row['Activities'] ||\n                                  row['أنشطة أخرى'] || row['الأنشطة'] || '').trim()\n\n      // البحث عن عمود التاريخ\n      const dateValue = row.Date || row.date || row['Date'] ||\n                       row['التاريخ'] || row['تاريخ'] || ''\n\n      // البحث عن عمود الوقت وتحويل إلى نص\n      const timeValue = String(row['Time of (Activities,Rotation,Workshop)'] ||\n                              row['Time'] || row.time || row['الوقت'] || '').trim()\n\n      console.log(`📋 القيم المستخرجة - الروتيشن: \"${rotationValue}\", ورشة: \"${workshopValue}\", نشاط: \"${activityValue}\", تاريخ: \"${dateValue}\", وقت: \"${timeValue}\"`)\n\n      // تحديد نوع الصف مع التحقق من صحة البيانات\n      if (rotationValue && typeof rotationValue === 'string' && rotationValue.includes('Start of Rotation')) {\n        // بداية روتيشن جديد\n        console.log('🔄 بداية روتيشن جديد:', rotationValue)\n        const rotationNumber = this.extractRotationNumber(rotationValue)\n        currentRotation = {\n          id: `rotation_${rotationCounter++}`,\n          number: rotationNumber,\n          name: `Rotation ${rotationNumber}`,\n          type: 'rotation_start',\n          startDate: this.parseDate(dateValue),\n          workshops: []\n        }\n\n        events.push({\n          id: currentRotation.id,\n          title: currentRotation.name,\n          description: `بداية ${currentRotation.name}`,\n          type: 'rotation_start',\n          date: currentRotation.startDate,\n          time: '09:00',\n          category: 'rotation',\n          status: 'scheduled',\n          rotation: currentRotation,\n          icon: '🔄',\n          color: 'blue'\n        })\n        console.log('✅ تم إضافة بداية روتيشن:', currentRotation.name)\n      } else if (rotationValue && typeof rotationValue === 'string' && rotationValue.includes('End of Rotation')) {\n        // نهاية روتيشن\n        console.log('🏁 نهاية روتيشن:', rotationValue)\n        if (currentRotation) {\n          const endDate = this.parseDate(dateValue)\n          events.push({\n            id: `${currentRotation.id}_end`,\n            title: `انتهاء ${currentRotation.name}`,\n            description: `نهاية ${currentRotation.name}`,\n            type: 'rotation_end',\n            date: endDate,\n            time: '17:00',\n            category: 'rotation',\n            status: 'scheduled',\n            rotation: currentRotation,\n            icon: '✅',\n            color: 'green'\n          })\n          console.log('✅ تم إضافة نهاية روتيشن:', currentRotation.name)\n        }\n      } else if (workshopValue && typeof workshopValue === 'string' && workshopValue.length > 0) {\n        // ورشة عمل\n        console.log('🎯 ورشة عمل:', workshopValue)\n        const workshop = {\n          id: `workshop_${Date.now()}_${index}`,\n          title: workshopValue,\n          description: workshopValue,\n          type: 'workshop',\n          date: this.parseDate(dateValue),\n          time: this.parseTime(timeValue),\n          duration: this.calculateDuration(timeValue),\n          category: 'workshop',\n          status: 'scheduled',\n          rotationId: currentRotation ? currentRotation.id : null,\n          rotationName: currentRotation ? currentRotation.name : null,\n          icon: '🎯',\n          color: 'purple'\n        }\n\n        if (currentRotation) {\n          // تجنب المرجع الدائري - احفظ فقط المعرف والاسم\n          currentRotation.workshops.push({\n            id: workshop.id,\n            title: workshop.title,\n            date: workshop.date,\n            time: workshop.time\n          })\n        }\n\n        events.push(workshop)\n        console.log('✅ تم إضافة ورشة عمل:', workshopValue)\n      } else if (activityValue && typeof activityValue === 'string' && activityValue.length > 0) {\n        // أنشطة أخرى\n        console.log('📋 نشاط آخر:', activityValue)\n        events.push({\n          id: `activity_${Date.now()}_${index}`,\n          title: activityValue,\n          description: activityValue,\n          type: 'activity',\n          date: this.parseDate(dateValue),\n          time: this.parseTime(timeValue),\n          duration: this.calculateDuration(timeValue),\n          category: 'activity',\n          status: 'scheduled',\n          rotationId: currentRotation ? currentRotation.id : null,\n          rotationName: currentRotation ? currentRotation.name : null,\n          icon: '📋',\n          color: 'orange'\n        })\n        console.log('✅ تم إضافة نشاط:', activityValue)\n      } else {\n        console.log('⚠️ صف فارغ أو غير مفهوم:', row)\n      }\n      } catch (error) {\n        console.error(`❌ خطأ في تحليل الصف ${index + 1}:`, error, row)\n        // متابعة معالجة الصفوف الأخرى\n      }\n    })\n\n    console.log('🎉 انتهى التحليل. تم إنشاء', events.length, 'حدث')\n    return events.sort((a, b) => new Date(a.date) - new Date(b.date))\n  }\n\n  // استخراج رقم الروتيشن\n  extractRotationNumber(rotationText) {\n    if (!rotationText || typeof rotationText !== 'string') {\n      console.log('⚠️ نص روتيشن غير صالح:', rotationText)\n      return 1\n    }\n\n    const patterns = [\n      /Rotation (\\d+)/i,\n      /روتيشن (\\d+)/,\n      /الروتيشن (\\d+)/,\n      /(\\d+)/  // أي رقم\n    ]\n\n    for (const pattern of patterns) {\n      const match = rotationText.match(pattern)\n      if (match && match[1]) {\n        const number = parseInt(match[1])\n        console.log('✅ رقم الروتيشن المستخرج:', number)\n        return number\n      }\n    }\n\n    console.log('⚠️ لم يتم العثور على رقم روتيشن، استخدام 1')\n    return 1\n  }\n\n  // تحليل التاريخ\n  parseDate(dateStr) {\n    console.log('📅 تحليل التاريخ:', dateStr)\n\n    if (!dateStr) {\n      console.log('⚠️ تاريخ فارغ، استخدام التاريخ الحالي')\n      return new Date().toISOString().split('T')[0]\n    }\n\n    // تحويل التاريخ من صيغة Excel\n    if (typeof dateStr === 'number') {\n      console.log('🔢 تاريخ Excel رقمي:', dateStr)\n      // Excel date serial number\n      const excelEpoch = new Date(1900, 0, 1)\n      const date = new Date(excelEpoch.getTime() + (dateStr - 2) * 24 * 60 * 60 * 1000)\n      const result = date.toISOString().split('T')[0]\n      console.log('✅ تاريخ محول:', result)\n      return result\n    }\n\n    // تحليل التاريخ النصي\n    const dateFormats = [\n      /(\\d{1,2})-(\\d{1,2})\\s+(\\w{3})/,  // 22-23 Dec\n      /(\\d{1,2})\\s+(\\w{3})/,            // 10 April\n      /(\\d{1,2})\\/(\\d{1,2})\\/(\\d{4})/,  // 22/12/2024\n      /(\\d{4})-(\\d{1,2})-(\\d{1,2})/,    // 2024-12-22\n    ]\n\n    for (const format of dateFormats) {\n      const match = dateStr.toString().match(format)\n      if (match) {\n        console.log('📝 تطابق مع النمط:', format, match)\n\n        if (match.length === 4 && match[3]) {\n          // نمط \"22-23 Dec\" أو \"10 April\"\n          const day = parseInt(match[1])\n          const month = match[3]\n          const year = new Date().getFullYear()\n          const monthNum = this.getMonthNumber(month)\n          const result = new Date(year, monthNum, day).toISOString().split('T')[0]\n          console.log('✅ تاريخ محول:', result)\n          return result\n        } else if (match.length === 4) {\n          // نمط \"22/12/2024\"\n          const day = parseInt(match[1])\n          const month = parseInt(match[2]) - 1\n          const year = parseInt(match[3])\n          const result = new Date(year, month, day).toISOString().split('T')[0]\n          console.log('✅ تاريخ محول:', result)\n          return result\n        } else if (match.length === 4) {\n          // نمط \"2024-12-22\"\n          const year = parseInt(match[1])\n          const month = parseInt(match[2]) - 1\n          const day = parseInt(match[3])\n          const result = new Date(year, month, day).toISOString().split('T')[0]\n          console.log('✅ تاريخ محول:', result)\n          return result\n        }\n      }\n    }\n\n    // محاولة تحليل التاريخ مباشرة\n    try {\n      const date = new Date(dateStr)\n      if (!isNaN(date.getTime())) {\n        const result = date.toISOString().split('T')[0]\n        console.log('✅ تاريخ محول مباشرة:', result)\n        return result\n      }\n    } catch (error) {\n      console.warn('⚠️ فشل في تحليل التاريخ:', dateStr, error)\n    }\n\n    console.log('⚠️ استخدام التاريخ الحالي كبديل')\n    return new Date().toISOString().split('T')[0]\n  }\n\n  // تحليل الوقت\n  parseTime(timeStr) {\n    console.log('🕐 تحليل الوقت:', timeStr)\n\n    if (!timeStr) {\n      console.log('⚠️ وقت فارغ، استخدام 09:00')\n      return '09:00'\n    }\n\n    // أنماط مختلفة للوقت\n    const timePatterns = [\n      /(\\d{1,2}):(\\d{2})\\s*(AM|PM)/i,           // 9:00 AM\n      /(\\d{1,2}):(\\d{2})/,                      // 9:00\n      /(\\d{1,2})\\s*(AM|PM)/i,                   // 9 AM\n      /(\\d{1,2}):(\\d{2})\\s*-\\s*(\\d{1,2}):(\\d{2})\\s*(AM|PM)/i  // 9:00 AM - 5:00 PM\n    ]\n\n    for (const pattern of timePatterns) {\n      const match = timeStr.toString().match(pattern)\n      if (match) {\n        console.log('🕐 تطابق مع نمط الوقت:', pattern, match)\n\n        let hours = parseInt(match[1])\n        const minutes = match[2] ? match[2] : '00'\n        const period = match[3] || match[5] // AM/PM\n\n        if (period && period.toUpperCase() === 'PM' && hours !== 12) {\n          hours += 12\n        } else if (period && period.toUpperCase() === 'AM' && hours === 12) {\n          hours = 0\n        }\n\n        const result = `${hours.toString().padStart(2, '0')}:${minutes}`\n        console.log('✅ وقت محول:', result)\n        return result\n      }\n    }\n\n    console.log('⚠️ استخدام الوقت الافتراضي 09:00')\n    return '09:00'\n  }\n\n  // حساب المدة\n  calculateDuration(timeStr) {\n    if (!timeStr) return 480 // 8 hours default\n    \n    const match = timeStr.match(/(\\d{1,2}):(\\d{2})\\s*AM\\s*-\\s*(\\d{1,2}):(\\d{2})\\s*PM/)\n    if (match) {\n      const startHour = parseInt(match[1])\n      const startMin = parseInt(match[2])\n      let endHour = parseInt(match[3])\n      const endMin = parseInt(match[4])\n      \n      if (endHour !== 12) endHour += 12\n      \n      const startMinutes = startHour * 60 + startMin\n      const endMinutes = endHour * 60 + endMin\n      \n      return endMinutes - startMinutes\n    }\n    \n    return 480 // 8 hours default\n  }\n\n  // تحويل اسم الشهر إلى رقم\n  getMonthNumber(monthName) {\n    if (!monthName || typeof monthName !== 'string') {\n      console.log('⚠️ اسم شهر غير صالح:', monthName)\n      return 0\n    }\n\n    const months = {\n      // إنجليزي مختصر\n      'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,\n      'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11,\n      // إنجليزي كامل\n      'January': 0, 'February': 1, 'March': 2, 'April': 3, 'May': 4, 'June': 5,\n      'July': 6, 'August': 7, 'September': 8, 'October': 9, 'November': 10, 'December': 11,\n      // عربي\n      'يناير': 0, 'فبراير': 1, 'مارس': 2, 'أبريل': 3, 'مايو': 4, 'يونيو': 5,\n      'يوليو': 6, 'أغسطس': 7, 'سبتمبر': 8, 'أكتوبر': 9, 'نوفمبر': 10, 'ديسمبر': 11\n    }\n\n    const monthKey = Object.keys(months).find(key =>\n      key.toLowerCase() === monthName.toLowerCase()\n    )\n\n    const result = monthKey ? months[monthKey] : 0\n    console.log('📅 تحويل الشهر:', monthName, '→', result)\n    return result\n  }\n\n  // حفظ أحداث التايم لاين\n  saveTimelineEvents(events) {\n    try {\n      // تنظيف البيانات من المراجع الدائرية\n      const cleanEvents = events.map(event => {\n        const cleanEvent = { ...event }\n\n        // إزالة المراجع الدائرية\n        if (cleanEvent.rotation && typeof cleanEvent.rotation === 'object') {\n          cleanEvent.rotationId = cleanEvent.rotation.id\n          cleanEvent.rotationName = cleanEvent.rotation.name\n          delete cleanEvent.rotation\n        }\n\n        // تنظيف المصفوفات الكبيرة\n        if (cleanEvent.assignedUsers && Array.isArray(cleanEvent.assignedUsers)) {\n          cleanEvent.assignedCount = cleanEvent.assignedUsers.length\n          delete cleanEvent.assignedUsers\n        }\n\n        if (cleanEvent.participants && Array.isArray(cleanEvent.participants)) {\n          cleanEvent.participantCount = cleanEvent.participants.length\n          delete cleanEvent.participants\n        }\n\n        return cleanEvent\n      })\n\n      localStorage.setItem(this.storageKey, JSON.stringify(cleanEvents))\n      console.log('✅ تم حفظ', cleanEvents.length, 'حدث في التايم لاين')\n\n      // حفظ الروتيشن منفصلة\n      const rotations = events.filter(event => event.type === 'rotation_start')\n        .map(event => ({\n          id: event.rotationId || event.id,\n          name: event.rotationName || event.title,\n          startDate: event.date,\n          workshops: []\n        }))\n      localStorage.setItem(this.rotationsKey, JSON.stringify(rotations))\n    } catch (error) {\n      console.error('خطأ في حفظ أحداث التايم لاين:', error)\n    }\n  }\n\n  // حذف جميع البيانات\n  clearAllData() {\n    try {\n      // حذف بيانات التايم لاين\n      localStorage.removeItem(this.storageKey)\n      localStorage.removeItem(this.rotationsKey)\n\n      // حذف البيانات الوهمية\n      localStorage.removeItem('timeline_events')\n      localStorage.removeItem('timeline_rotations')\n      localStorage.removeItem('timeline_data')\n      localStorage.removeItem('sample_timeline_events')\n\n      // إعادة تعيين البيانات الحقيقية فقط\n      localStorage.setItem(this.storageKey, JSON.stringify([]))\n      localStorage.setItem(this.rotationsKey, JSON.stringify([]))\n\n      console.log('✅ تم حذف جميع البيانات الوهمية وإعادة تعيين التايم لاين')\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في حذف البيانات:', error)\n      return false\n    }\n  }\n\n  // تحميل أحداث التايم لاين\n  getTimelineEvents() {\n    try {\n      const stored = localStorage.getItem(this.storageKey)\n      return stored ? JSON.parse(stored) : []\n    } catch (error) {\n      console.error('خطأ في تحميل أحداث التايم لاين:', error)\n      return []\n    }\n  }\n\n  // تحميل الروتيشن\n  getRotations() {\n    const stored = localStorage.getItem(this.rotationsKey)\n    return stored ? JSON.parse(stored) : []\n  }\n\n  // مزامنة ورش العمل مع التايم لاين\n  syncWorkshopsToTimeline() {\n    try {\n      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')\n      const events = this.getTimelineEvents()\n\n      workshops.forEach(workshop => {\n        // التحقق من عدم وجود الورشة في التايم لاين\n        const existingEvent = events.find(event =>\n          event.type === 'workshop' && event.sourceId === workshop.id\n        )\n\n        if (!existingEvent) {\n          const workshopEvent = {\n            id: `workshop_${workshop.id}`,\n            sourceId: workshop.id,\n            title: workshop.title,\n            description: workshop.description || workshop.title,\n            type: 'workshop',\n            date: workshop.date,\n            time: workshop.time || '09:00',\n            duration: workshop.duration || 480,\n            category: 'workshop',\n            status: workshop.status || 'scheduled',\n            icon: '🎯',\n            color: 'purple',\n            location: workshop.location,\n            instructor: workshop.instructor,\n            createdAt: new Date().toISOString()\n          }\n\n          events.push(workshopEvent)\n          console.log('✅ تم إضافة ورشة عمل للتايم لاين:', workshop.title)\n        }\n      })\n\n      this.saveTimelineEvents(events)\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في مزامنة ورش العمل:', error)\n      return false\n    }\n  }\n\n  // مزامنة المهام مع التايم لاين\n  syncTasksToTimeline() {\n    try {\n      const tasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')\n      const events = this.getTimelineEvents()\n\n      tasks.forEach(task => {\n        // التحقق من عدم وجود المهمة في التايم لاين\n        const existingEvent = events.find(event =>\n          event.type === 'task' && event.sourceId === task.id\n        )\n\n        if (!existingEvent) {\n          const taskEvent = {\n            id: `task_${task.id}`,\n            sourceId: task.id,\n            title: task.title,\n            description: task.description || task.title,\n            type: 'task',\n            date: task.dueDate || task.date,\n            time: task.time || '09:00',\n            duration: task.estimatedTime || 60,\n            category: 'task',\n            status: task.status || 'pending',\n            priority: task.priority || 'medium',\n            icon: '📋',\n            color: task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'orange' : 'blue',\n            assignedTo: task.assignedTo,\n            createdAt: new Date().toISOString()\n          }\n\n          events.push(taskEvent)\n          console.log('✅ تم إضافة مهمة للتايم لاين:', task.title)\n        }\n      })\n\n      this.saveTimelineEvents(events)\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في مزامنة المهام:', error)\n      return false\n    }\n  }\n\n  // مزامنة تلقائية عند إضافة ورشة عمل\n  addWorkshopToTimeline(workshop) {\n    try {\n      const events = this.getTimelineEvents()\n\n      const workshopEvent = {\n        id: `workshop_${workshop.id}`,\n        sourceId: workshop.id,\n        title: workshop.title,\n        description: workshop.description || workshop.title,\n        type: 'workshop',\n        date: workshop.date,\n        time: workshop.time || '09:00',\n        duration: workshop.duration || 480,\n        category: 'workshop',\n        status: workshop.status || 'scheduled',\n        icon: '🎯',\n        color: 'purple',\n        location: workshop.location,\n        instructor: workshop.instructor,\n        createdAt: new Date().toISOString()\n      }\n\n      events.push(workshopEvent)\n      this.saveTimelineEvents(events)\n      console.log('✅ تم إضافة ورشة عمل جديدة للتايم لاين:', workshop.title)\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في إضافة ورشة العمل للتايم لاين:', error)\n      return false\n    }\n  }\n\n  // مزامنة تلقائية عند إضافة مهمة\n  addTaskToTimeline(task) {\n    try {\n      const events = this.getTimelineEvents()\n\n      const taskEvent = {\n        id: `task_${task.id}`,\n        sourceId: task.id,\n        title: task.title,\n        description: task.description || task.title,\n        type: 'task',\n        date: task.dueDate || task.date,\n        time: task.time || '09:00',\n        duration: task.estimatedTime || 60,\n        category: 'task',\n        status: task.status || 'pending',\n        priority: task.priority || 'medium',\n        icon: '📋',\n        color: task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'orange' : 'blue',\n        assignedTo: task.assignedTo,\n        createdAt: new Date().toISOString()\n      }\n\n      events.push(taskEvent)\n      this.saveTimelineEvents(events)\n      console.log('✅ تم إضافة مهمة جديدة للتايم لاين:', task.title)\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في إضافة المهمة للتايم لاين:', error)\n      return false\n    }\n  }\n\n  // الحصول على اسم الحالة للعرض\n  getStatusDisplayName(status) {\n    const statusNames = {\n      'pending': 'في الانتظار',\n      'active': 'نشط',\n      'completed': 'مكتمل',\n      'cancelled': 'ملغي',\n      'in_progress': 'قيد التنفيذ',\n      'scheduled': 'مجدول',\n      'published': 'منشور',\n      'draft': 'مسودة',\n      'submitted': 'تم التسليم',\n      'graded': 'تم التقييم',\n      'overdue': 'متأخر',\n      'urgent': 'عاجل'\n    }\n    return statusNames[status] || status || 'غير محدد'\n  }\n\n  // إضافة حدث جديد\n  addEvent(event) {\n    const events = this.getTimelineEvents()\n\n    // التحقق من عدم وجود حدث مماثل\n    const existingEvent = events.find(e =>\n      e.sourceId === event.sourceId &&\n      e.type === event.type &&\n      e.date === event.date\n    )\n\n    if (existingEvent) {\n      console.log('⚠️ الحدث موجود بالفعل في التايم لاين:', event.title)\n      return existingEvent\n    }\n\n    const newEvent = {\n      ...event,\n      id: event.id || `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    events.push(newEvent)\n    events.sort((a, b) => new Date(a.date) - new Date(b.date))\n    this.saveTimelineEvents(events)\n\n    console.log('✅ تم إضافة حدث جديد للتايم لاين:', newEvent.title)\n    return newEvent\n  }\n\n  // تحديث حدث\n  updateEvent(eventId, updates) {\n    const events = this.getTimelineEvents()\n    const index = events.findIndex(event => event.id === eventId)\n    \n    if (index !== -1) {\n      events[index] = {\n        ...events[index],\n        ...updates,\n        updatedAt: new Date().toISOString()\n      }\n      \n      events.sort((a, b) => new Date(a.date) - new Date(b.date))\n      this.saveTimelineEvents(events)\n      \n      return events[index]\n    }\n    \n    return null\n  }\n\n  // حذف حدث\n  deleteEvent(eventId) {\n    const events = this.getTimelineEvents()\n    const filteredEvents = events.filter(event => event.id !== eventId)\n    this.saveTimelineEvents(filteredEvents)\n    \n    return true\n  }\n\n  // تصدير التايم لاين إلى Excel مع تنسيق محسن\n  async exportToExcel() {\n    try {\n      // استيراد مكتبة ExcelJS للتنسيق المتقدم\n      const ExcelJS = (await import('exceljs')).default\n      const { saveAs } = await import('file-saver')\n\n      const events = this.getTimelineEvents()\n\n      // الحصول على ألوان النظام\n      const getUserThemeColors = () => {\n        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')\n        const userId = currentUser.id || currentUser.email || 'default'\n\n        const primaryColor = localStorage.getItem(`primaryColor_${userId}`) || '#B89966'\n        const secondaryColor = localStorage.getItem(`secondaryColor_${userId}`) || '#94BCCB'\n\n        return {\n          primary: primaryColor.replace('#', ''),\n          secondary: secondaryColor.replace('#', ''),\n          accent: 'F5F5DC',\n          text: '333333',\n          lightGray: 'F8F9FA'\n        }\n      }\n\n      const colors = getUserThemeColors()\n\n      // إنشاء workbook جديد\n      const workbook = new ExcelJS.Workbook()\n      workbook.creator = 'نظام إدارة التدريب'\n      workbook.lastModifiedBy = 'نظام إدارة التدريب'\n      workbook.created = new Date()\n      workbook.modified = new Date()\n\n      const worksheet = workbook.addWorksheet('التايم لاين التدريبي')\n\n      // إعداد العناوين\n      const headers = [\n        'التاريخ',\n        'الوقت',\n        'العنوان',\n        'الوصف',\n        'النوع',\n        'الفئة',\n        'الحالة',\n        'تاريخ البداية',\n        'تاريخ النهاية',\n        'الروتيشن'\n      ]\n\n      const headerRow = worksheet.addRow(headers)\n\n      // تنسيق العناوين\n      headerRow.eachCell((cell) => {\n        cell.font = { bold: true, color: { argb: 'FFFFFF' } }\n        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.primary } }\n        cell.alignment = { horizontal: 'center', vertical: 'middle' }\n        cell.border = {\n          top: { style: 'thin' },\n          left: { style: 'thin' },\n          bottom: { style: 'thin' },\n          right: { style: 'thin' }\n        }\n      })\n\n      // إضافة البيانات\n      events.forEach(event => {\n        const row = worksheet.addRow([\n          event.date || '-',\n          event.time || '-',\n          event.title || '-',\n          event.description || '-',\n          this.getTypeDisplayName(event.type),\n          this.getCategoryDisplayName(event.category),\n          this.getStatusDisplayName(event.status),\n          event.startDate || '-',\n          event.endDate || event.dueDate || '-',\n          event.rotation ? event.rotation.name : '-'\n        ])\n\n        // تنسيق الصفوف\n        row.eachCell((cell) => {\n          cell.border = {\n            top: { style: 'thin' },\n            left: { style: 'thin' },\n            bottom: { style: 'thin' },\n            right: { style: 'thin' }\n          }\n          cell.alignment = { horizontal: 'center', vertical: 'middle' }\n        })\n\n        // تلوين الصفوف بناءً على الحالة\n        if (event.status === 'completed') {\n          row.eachCell((cell) => {\n            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'E8F5E8' } }\n          })\n        } else if (event.status === 'overdue') {\n          row.eachCell((cell) => {\n            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8E8' } }\n          })\n        } else if (event.status === 'active') {\n          row.eachCell((cell) => {\n            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.lightGray } }\n          })\n        }\n      })\n\n      // تعديل عرض الأعمدة\n      worksheet.columns = [\n        { width: 15 }, // التاريخ\n        { width: 10 }, // الوقت\n        { width: 30 }, // العنوان\n        { width: 40 }, // الوصف\n        { width: 15 }, // النوع\n        { width: 15 }, // الفئة\n        { width: 15 }, // الحالة\n        { width: 15 }, // تاريخ البداية\n        { width: 15 }, // تاريخ النهاية\n        { width: 20 }  // الروتيشن\n      ]\n\n      // حفظ الملف\n      const buffer = await workbook.xlsx.writeBuffer()\n      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n      const fileName = `timeline_${new Date().toISOString().split('T')[0]}.xlsx`\n\n      saveAs(blob, fileName)\n\n      console.log('✅ تم تصدير التايم لاين إلى Excel بنجاح')\n\n    } catch (error) {\n      console.error('❌ خطأ في تصدير التايم لاين:', error)\n\n      // fallback إلى الطريقة القديمة\n      const events = this.getTimelineEvents()\n\n      const exportData = events.map(event => ({\n        'التاريخ': event.date,\n        'الوقت': event.time,\n        'العنوان': event.title,\n        'الوصف': event.description,\n        'النوع': this.getTypeDisplayName(event.type),\n        'الفئة': this.getCategoryDisplayName(event.category),\n        'الحالة': this.getStatusDisplayName(event.status),\n        'تاريخ البداية': event.startDate || '-',\n        'تاريخ النهاية': event.endDate || event.dueDate || '-',\n        'الروتيشن': event.rotation ? event.rotation.name : '-'\n      }))\n\n      const ws = XLSX.utils.json_to_sheet(exportData)\n      const wb = XLSX.utils.book_new()\n      XLSX.utils.book_append_sheet(wb, ws, 'Timeline')\n\n      const fileName = `timeline_${new Date().toISOString().split('T')[0]}.xlsx`\n      XLSX.writeFile(wb, fileName)\n    }\n  }\n\n  // أسماء عرض الأنواع\n  getTypeDisplayName(type) {\n    const types = {\n      'rotation_start': 'بداية روتيشن',\n      'rotation_end': 'نهاية روتيشن',\n      'workshop': 'ورشة عمل',\n      'activity': 'نشاط',\n      'task': 'مهمة'\n    }\n    return types[type] || type\n  }\n\n  // أسماء عرض الفئات\n  getCategoryDisplayName(category) {\n    const categories = {\n      'rotation': 'روتيشن',\n      'workshop': 'ورشة عمل',\n      'activity': 'نشاط',\n      'task': 'مهمة'\n    }\n    return categories[category] || category\n  }\n\n  // أسماء عرض الحالات\n  getStatusDisplayName(status) {\n    const statuses = {\n      'scheduled': 'مجدول',\n      'in_progress': 'قيد التنفيذ',\n      'completed': 'مكتمل',\n      'cancelled': 'ملغي',\n      'overdue': 'متأخر',\n      'urgent': 'عاجل',\n      'pending': 'في الانتظار'\n    }\n    return statuses[status] || status\n  }\n\n  // دالة مزامنة البيانات من الأنظمة إلى التايم لاين (للاستخدام في صفحة التايم لاين)\n  syncSystemDataToTimeline() {\n    console.log('🔄 بدء مزامنة البيانات من الأنظمة إلى التايم لاين...')\n\n    try {\n      // مزامنة من الأنظمة الأخرى إلى التايم لاين\n      this.syncWithTasks()\n      this.syncWithWorkshops()\n      this.syncWithCourses()\n      this.syncWithRotations()\n\n      console.log('✅ تمت مزامنة البيانات من الأنظمة إلى التايم لاين بنجاح')\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في مزامنة البيانات من الأنظمة إلى التايم لاين:', error)\n      return false\n    }\n  }\n\n  // مزامنة شاملة مع جميع البيانات\n  syncAllData() {\n    console.log('🔄 بدء المزامنة الشاملة للتايم لاين...')\n\n    try {\n      // مزامنة من الأنظمة الأخرى إلى التايم لاين\n      this.syncWithTasks()\n      this.syncWithWorkshops()\n      this.syncWithCourses()\n      this.syncWithRotations()\n\n      // مزامنة من التايم لاين إلى الأنظمة الأخرى\n      this.syncTimelineToSystems()\n\n      console.log('✅ تمت المزامنة الشاملة بنجاح')\n      return true\n    } catch (error) {\n      console.error('❌ فشل في المزامنة الشاملة:', error)\n      return false\n    }\n  }\n\n  // مزامنة من التايم لاين إلى الأنظمة الأخرى\n  syncTimelineToSystems() {\n    console.log('🔄 مزامنة من التايم لاين إلى الأنظمة الأخرى...')\n\n    const events = this.getTimelineEvents()\n\n    // مزامنة ورش العمل\n    this.syncTimelineWorkshopsToSystem(events)\n\n    // مزامنة المهام\n    this.syncTimelineTasksToSystem(events)\n\n    // مزامنة الدورات\n    this.syncTimelineCoursesToSystem(events)\n  }\n\n  // مزامنة ورش العمل من التايم لاين إلى النظام\n  syncTimelineWorkshopsToSystem(events) {\n    try {\n      const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')\n      const timelineWorkshops = events.filter(event => event.type === 'workshop' && !event.sourceId)\n\n      console.log('🎯 مزامنة ورش العمل من التايم لاين:', timelineWorkshops.length)\n\n      timelineWorkshops.forEach(event => {\n        // التحقق من عدم وجود ورشة مماثلة\n        const existingWorkshop = workshops.find(w => w.title === event.title && w.startDate === event.date)\n\n        if (!existingWorkshop) {\n          const newWorkshop = {\n            id: `workshop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            title: event.title,\n            description: event.description || 'ورشة عمل مستوردة من التايم لاين',\n            startDate: event.date,\n            startTime: event.time || '09:00',\n            duration: event.duration || 480,\n            status: 'published',\n            trainer: 'مستورد من التايم لاين',\n            selectedTrainees: [],\n            createdAt: new Date().toISOString(),\n            source: 'timeline'\n          }\n\n          workshops.push(newWorkshop)\n          console.log('➕ تم إضافة ورشة عمل جديدة:', newWorkshop.title)\n\n          // تحديث sourceId في التايم لاين\n          event.sourceId = newWorkshop.id\n        }\n      })\n\n      localStorage.setItem('workshops_data', JSON.stringify(workshops))\n      this.saveTimelineEvents(events) // حفظ التحديثات\n\n    } catch (error) {\n      console.error('❌ خطأ في مزامنة ورش العمل:', error)\n    }\n  }\n\n  // مزامنة المهام من التايم لاين إلى النظام\n  syncTimelineTasksToSystem(events) {\n    try {\n      const tasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')\n      const timelineTasks = events.filter(event => event.type === 'task' && !event.sourceId)\n\n      console.log('📝 مزامنة المهام من التايم لاين:', timelineTasks.length)\n\n      timelineTasks.forEach(event => {\n        // التحقق من عدم وجود مهمة مماثلة\n        const existingTask = tasks.find(t => t.title === event.title && t.dueDate === event.date)\n\n        if (!existingTask) {\n          const newTask = {\n            id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            title: event.title,\n            description: event.description || 'مهمة مستوردة من التايم لاين',\n            dueDate: event.date,\n            dueTime: event.time || '23:59',\n            status: 'published',\n            course: 'مستورد من التايم لاين',\n            trainer: 'مستورد من التايم لاين',\n            assignedUsers: [],\n            createdAt: new Date().toISOString(),\n            source: 'timeline'\n          }\n\n          tasks.push(newTask)\n          console.log('➕ تم إضافة مهمة جديدة:', newTask.title)\n\n          // تحديث sourceId في التايم لاين\n          event.sourceId = newTask.id\n        }\n      })\n\n      localStorage.setItem('tasks_data', JSON.stringify(tasks))\n      this.saveTimelineEvents(events) // حفظ التحديثات\n\n    } catch (error) {\n      console.error('❌ خطأ في مزامنة المهام:', error)\n    }\n  }\n\n  // مزامنة الدورات من التايم لاين إلى النظام\n  syncTimelineCoursesToSystem(events) {\n    try {\n      const courses = JSON.parse(localStorage.getItem('courses_data') || '[]')\n      const timelineCourses = events.filter(event => event.type === 'course' && !event.sourceId)\n\n      console.log('📚 مزامنة الدورات من التايم لاين:', timelineCourses.length)\n\n      timelineCourses.forEach(event => {\n        // التحقق من عدم وجود دورة مماثلة\n        const existingCourse = courses.find(c => c.title === event.title && c.startDate === event.date)\n\n        if (!existingCourse) {\n          const newCourse = {\n            id: `course_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            title: event.title,\n            description: event.description || 'دورة مستوردة من التايم لاين',\n            startDate: event.date,\n            startTime: event.time || '09:00',\n            duration: event.duration || 480,\n            status: 'published',\n            instructor: 'مستورد من التايم لاين',\n            enrolledStudents: [],\n            createdAt: new Date().toISOString(),\n            source: 'timeline'\n          }\n\n          courses.push(newCourse)\n          console.log('➕ تم إضافة دورة جديدة:', newCourse.title)\n\n          // تحديث sourceId في التايم لاين\n          event.sourceId = newCourse.id\n        }\n      })\n\n      localStorage.setItem('courses_data', JSON.stringify(courses))\n      this.saveTimelineEvents(events) // حفظ التحديثات\n\n    } catch (error) {\n      console.error('❌ خطأ في مزامنة الدورات:', error)\n    }\n  }\n\n  // مزامنة مع المهام\n  syncWithTasks() {\n    try {\n      // استخدام نفس مفتاح التخزين المستخدم في النظام\n      const tasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')\n      const events = this.getTimelineEvents()\n      const rotations = JSON.parse(localStorage.getItem('rotations_data') || '[]')\n\n      console.log('🔄 مزامنة المهام:', tasks.length, 'مهمة')\n\n      tasks.forEach(task => {\n        // فحص القائمة السوداء\n        try {\n          const blacklist = JSON.parse(localStorage.getItem('task_blacklist') || '[]')\n          if (blacklist.some(blacklistedTitle => task.title && task.title.includes(blacklistedTitle))) {\n            console.log('🚫 تجاهل مهمة في القائمة السوداء:', task.title)\n            return\n          }\n        } catch (error) {\n          console.error('خطأ في فحص القائمة السوداء:', error)\n        }\n\n        // تم إزالة فحص المهام الوهمية - لا توجد مهام وهمية في النظام\n\n        // التحقق من وجود أحداث المهمة (بداية ونهاية)\n        const existingStartEvent = events.find(event =>\n          event.type === 'task_start' && event.sourceId === task.id\n        )\n        const existingDueEvent = events.find(event =>\n          event.type === 'task_due' && event.sourceId === task.id\n        )\n\n        // إضافة بداية المهمة إذا كان لها تاريخ بداية\n        if (!existingStartEvent && task.startDate) {\n          const startColor = this.getEventColorByRotation(task.startDate, rotations)\n          console.log('➕ إضافة بداية المهمة للتايم لاين:', task.title)\n          this.addEvent({\n            title: this.createEventTitle('start', task.title),\n            description: task.description || this.createEventDescription('taskStart'),\n            type: 'task_start',\n            date: task.startDate,\n            time: task.startTime || '09:00',\n            category: 'task',\n            status: task.status === 'published' ? 'scheduled' : 'draft',\n            sourceId: task.id,\n            icon: '🚀',\n            color: startColor,\n            course: task.course,\n            trainer: task.trainer,\n            assignedCount: task.assignedUsers ? task.assignedUsers.length : 0\n          })\n        }\n\n        // إضافة موعد تسليم المهمة\n        if (!existingDueEvent && task.dueDate) {\n          const dueColor = this.getEventColorByRotation(task.dueDate, rotations)\n          console.log('➕ إضافة موعد تسليم المهمة للتايم لاين:', task.title)\n          this.addEvent({\n            title: this.createEventTitle('due', task.title),\n            description: task.description || this.createEventDescription('taskDue'),\n            type: 'task_due',\n            date: task.dueDate,\n            time: task.dueTime || '23:59',\n            category: 'task',\n            status: task.status === 'published' ? 'scheduled' : 'draft',\n            sourceId: task.id,\n            icon: '📝',\n            color: dueColor,\n            course: task.course,\n            trainer: task.trainer,\n            assignedCount: task.assignedUsers ? task.assignedUsers.length : 0\n          })\n        }\n      })\n    } catch (error) {\n      console.error('خطأ في مزامنة المهام:', error)\n    }\n  }\n\n  // مزامنة مع ورش العمل\n  syncWithWorkshops() {\n    try {\n      // تحميل ورش العمل من مصادر متعددة\n      const workshopsFromStorage = JSON.parse(localStorage.getItem('app_workshops') || '[]')\n\n      // محاولة تحميل من dataStorage أيضاً\n      let workshopsFromDataStorage = []\n      try {\n        const { default: dataStorage } = require('../utils/dataStorage')\n        workshopsFromDataStorage = dataStorage.loadWorkshops() || []\n      } catch (error) {\n        console.log('⚠️ لا يمكن تحميل من dataStorage:', error.message)\n      }\n\n      // دمج ورش العمل من المصادر المختلفة\n      const allWorkshops = [...workshopsFromStorage, ...workshopsFromDataStorage]\n\n      // إزالة المكررات\n      const uniqueWorkshops = allWorkshops.reduce((acc, current) => {\n        const existingWorkshop = acc.find(workshop => workshop.id === current.id)\n        if (!existingWorkshop) {\n          acc.push(current)\n        }\n        return acc\n      }, [])\n\n      const events = this.getTimelineEvents()\n      const rotations = JSON.parse(localStorage.getItem('rotations_data') || '[]')\n\n      console.log('🔄 مزامنة ورش العمل:', uniqueWorkshops.length, 'ورشة')\n\n      uniqueWorkshops.forEach(workshop => {\n        // التحقق من وجود أحداث ورشة العمل (بداية ونهاية)\n        const existingStartEvent = events.find(event =>\n          event.type === 'workshop_start' && event.sourceId === workshop.id\n        )\n        const existingEndEvent = events.find(event =>\n          event.type === 'workshop_end' && event.sourceId === workshop.id\n        )\n\n        // إضافة بداية ورشة العمل\n        if (!existingStartEvent && workshop.startDate) {\n          const startColor = this.getEventColorByRotation(workshop.startDate, rotations)\n          console.log('➕ إضافة بداية ورشة العمل للتايم لاين:', workshop.title)\n          this.addEvent({\n            title: this.createEventTitle('start', workshop.title),\n            description: workshop.description || this.createEventDescription('workshopStart'),\n            type: 'workshop_start',\n            date: workshop.startDate,\n            time: workshop.startTime || '09:00',\n            category: 'workshop',\n            status: workshop.status === 'published' ? 'scheduled' : 'draft',\n            sourceId: workshop.id,\n            icon: '🚀',\n            color: startColor,\n            trainer: workshop.trainer,\n            participantCount: workshop.selectedTrainees ? workshop.selectedTrainees.length : 0\n          })\n        }\n\n        // إضافة نهاية ورشة العمل\n        if (!existingEndEvent && workshop.endDate) {\n          const endColor = this.getEventColorByRotation(workshop.endDate, rotations)\n          console.log('➕ إضافة نهاية ورشة العمل للتايم لاين:', workshop.title)\n          this.addEvent({\n            title: this.createEventTitle('end', workshop.title),\n            description: workshop.description || this.createEventDescription('workshopEnd'),\n            type: 'workshop_end',\n            date: workshop.endDate,\n            time: workshop.endTime || '17:00',\n            category: 'workshop',\n            status: workshop.status === 'published' ? 'scheduled' : 'draft',\n            sourceId: workshop.id,\n            icon: '🎯',\n            color: endColor,\n            trainer: workshop.trainer,\n            participantCount: workshop.selectedTrainees ? workshop.selectedTrainees.length : 0\n          })\n        }\n      })\n    } catch (error) {\n      console.error('خطأ في مزامنة ورش العمل:', error)\n    }\n  }\n\n  // مزامنة مع الدورات\n  syncWithCourses() {\n    try {\n      // استخدام نفس مفتاح التخزين المستخدم في النظام\n      const courses = JSON.parse(localStorage.getItem('app_courses') || '[]')\n      const events = this.getTimelineEvents()\n      const rotations = JSON.parse(localStorage.getItem('rotations_data') || '[]')\n\n      console.log('📚 الدورات الموجودة في النظام:', courses.length)\n      console.log('📚 تفاصيل الدورات:', courses.map(c => ({ id: c.id, title: c.courseName || c.title, startDate: c.startDate, endDate: c.endDate })))\n\n      console.log('🔄 مزامنة الدورات:', courses.length, 'دورة')\n\n      courses.forEach(course => {\n        // التحقق من وجود أحداث الدورة (بداية ونهاية)\n        const existingStartEvent = events.find(event =>\n          event.type === 'course_start' && event.sourceId === course.id\n        )\n        const existingEndEvent = events.find(event =>\n          event.type === 'course_end' && event.sourceId === course.id\n        )\n\n        // إضافة بداية الدورة\n        if (!existingStartEvent && course.startDate) {\n          const startColor = this.getEventColorByRotation(course.startDate, rotations)\n          console.log('➕ إضافة بداية الدورة للتايم لاين:', course.title)\n          this.addEvent({\n            title: this.createEventTitle('start', course.courseName || course.title),\n            description: course.description || this.createEventDescription('courseStart'),\n            type: 'course_start',\n            date: course.startDate,\n            time: course.startTime || '09:00',\n            category: 'course',\n            status: course.status === 'published' ? 'scheduled' : 'draft',\n            sourceId: course.id,\n            icon: '🚀',\n            color: startColor,\n            instructor: course.trainer || course.instructor,\n            enrolledCount: course.selectedTrainees ? course.selectedTrainees.length : 0\n          })\n        }\n\n        // إضافة نهاية الدورة\n        if (!existingEndEvent && course.endDate) {\n          const endColor = this.getEventColorByRotation(course.endDate, rotations)\n          console.log('➕ إضافة نهاية الدورة للتايم لاين:', course.title)\n          this.addEvent({\n            title: this.createEventTitle('end', course.courseName || course.title),\n            description: course.description || this.createEventDescription('courseEnd'),\n            type: 'course_end',\n            date: course.endDate,\n            time: course.endTime || '17:00',\n            category: 'course',\n            status: course.status === 'published' ? 'scheduled' : 'draft',\n            sourceId: course.id,\n            icon: '📚',\n            color: endColor,\n            instructor: course.trainer || course.instructor,\n            enrolledCount: course.selectedTrainees ? course.selectedTrainees.length : 0\n          })\n        }\n      })\n    } catch (error) {\n      console.error('خطأ في مزامنة الدورات:', error)\n    }\n  }\n\n  // مزامنة مع الروتيشن\n  syncWithRotations() {\n    const rotations = JSON.parse(localStorage.getItem('app_rotations') || '[]')\n    const events = this.getTimelineEvents()\n\n    rotations.forEach(rotation => {\n      // بداية الروتيشن\n      const existingStartEvent = events.find(event =>\n        event.type === 'rotation_start' && event.sourceId === rotation.id\n      )\n\n      if (!existingStartEvent) {\n        this.addEvent({\n          title: this.createEventTitle('start', rotation.name),\n          description: rotation.description || this.createEventDescription('rotationStart'),\n          type: 'rotation_start',\n          date: rotation.startDate,\n          time: rotation.startTime || '09:00',\n          category: 'rotation',\n          status: 'scheduled',\n          sourceId: rotation.id,\n          icon: '🔄',\n          color: 'orange',\n          rotation: rotation\n        })\n      }\n\n      // نهاية الروتيشن\n      const existingEndEvent = events.find(event =>\n        event.type === 'rotation_end' && event.sourceId === rotation.id\n      )\n\n      if (!existingEndEvent && rotation.endDate) {\n        this.addEvent({\n          title: this.createEventTitle('end', rotation.name),\n          description: this.createEventDescription('rotationEnd'),\n          type: 'rotation_end',\n          date: rotation.endDate,\n          time: rotation.endTime || '17:00',\n          category: 'rotation',\n          status: 'scheduled',\n          sourceId: rotation.id,\n          icon: '✅',\n          color: 'green',\n          rotation: rotation\n        })\n      }\n    })\n  }\n\n\n\n  // حذف حدث من التايم لاين بناءً على sourceId\n  removeEventBySourceId(sourceId, eventType) {\n    try {\n      const events = this.getTimelineEvents()\n      const updatedEvents = events.filter(event =>\n        !(event.sourceId === sourceId && event.type === eventType)\n      )\n      this.saveTimelineEvents(updatedEvents)\n      console.log(`✅ تم حذف الحدث من التايم لاين: ${sourceId}`)\n      return true\n    } catch (error) {\n      console.error('خطأ في حذف الحدث من التايم لاين:', error)\n      return false\n    }\n  }\n\n  // حذف جميع الأحداث المرتبطة بمهمة معينة\n  removeTaskEvents(taskId) {\n    try {\n      const events = this.getTimelineEvents()\n      const updatedEvents = events.filter(event => {\n        const isTaskEvent = event.category === 'task' || event.type?.includes('task')\n        const matchesTaskId = event.sourceId === taskId ||\n                             event.sourceId === `${taskId}_start` ||\n                             event.sourceId === `${taskId}_due` ||\n                             event.sourceId === taskId.toString()\n\n        return !(isTaskEvent && matchesTaskId)\n      })\n      this.saveTimelineEvents(updatedEvents)\n      console.log(`✅ تم حذف جميع أحداث المهمة من التايم لاين: ${taskId}`)\n      return true\n    } catch (error) {\n      console.error('خطأ في حذف أحداث المهمة من التايم لاين:', error)\n      return false\n    }\n  }\n\n  // حذف جميع الأحداث المرتبطة بورشة عمل معينة\n  removeWorkshopEvents(workshopId) {\n    try {\n      console.log('🗑️ بدء حذف أحداث ورشة العمل من التايم لاين:', workshopId)\n\n      const events = this.getTimelineEvents()\n      console.log('📊 إجمالي الأحداث قبل الحذف:', events.length)\n\n      // البحث عن الأحداث المرتبطة بورشة العمل\n      const workshopEvents = events.filter(event => {\n        const isWorkshopEvent = event.category === 'workshop' || event.type?.includes('workshop')\n        const matchesWorkshopId = event.sourceId === workshopId ||\n                                 event.sourceId === `${workshopId}_start` ||\n                                 event.sourceId === `${workshopId}_end` ||\n                                 event.sourceId === workshopId.toString() ||\n                                 event.sourceId === parseInt(workshopId) ||\n                                 event.title?.includes(workshopId)\n\n        return isWorkshopEvent && matchesWorkshopId\n      })\n\n      console.log('🔍 الأحداث المرتبطة بورشة العمل:', {\n        workshopId,\n        foundEvents: workshopEvents.length,\n        eventTitles: workshopEvents.map(e => e.title)\n      })\n\n      const updatedEvents = events.filter(event => {\n        const isWorkshopEvent = event.category === 'workshop' || event.type?.includes('workshop')\n        const matchesWorkshopId = event.sourceId === workshopId ||\n                                 event.sourceId === `${workshopId}_start` ||\n                                 event.sourceId === `${workshopId}_end` ||\n                                 event.sourceId === workshopId.toString() ||\n                                 event.sourceId === parseInt(workshopId) ||\n                                 event.title?.includes(workshopId)\n\n        return !(isWorkshopEvent && matchesWorkshopId)\n      })\n\n      console.log('📊 إجمالي الأحداث بعد الحذف:', updatedEvents.length)\n      console.log('✅ تم حذف', events.length - updatedEvents.length, 'حدث من التايم لاين')\n\n      this.saveTimelineEvents(updatedEvents)\n      console.log(`✅ تم حذف جميع أحداث ورشة العمل من التايم لاين: ${workshopId}`)\n      return true\n    } catch (error) {\n      console.error('❌ خطأ في حذف أحداث ورشة العمل من التايم لاين:', error)\n      return false\n    }\n  }\n\n  // حذف جميع الأحداث من نوع معين\n  removeEventsByType(eventType) {\n    try {\n      const events = this.getTimelineEvents()\n      const updatedEvents = events.filter(event => event.type !== eventType)\n      this.saveTimelineEvents(updatedEvents)\n      console.log(`✅ تم حذف جميع أحداث ${eventType} من التايم لاين`)\n      return true\n    } catch (error) {\n      console.error(`خطأ في حذف أحداث ${eventType} من التايم لاين:`, error)\n      return false\n    }\n  }\n\n  // مسح التايم لاين بالكامل\n  clearAllEvents() {\n    try {\n      localStorage.removeItem(this.storageKey)\n      localStorage.removeItem(this.rotationsKey)\n      localStorage.setItem(this.storageKey, JSON.stringify([]))\n      console.log('✅ تم مسح جميع أحداث التايم لاين')\n      return true\n    } catch (error) {\n      console.error('خطأ في مسح التايم لاين:', error)\n      return false\n    }\n  }\n\n  // إعادة تعيين التايم لاين\n  resetTimeline() {\n    console.log('🔄 إعادة تعيين التايم لاين...')\n    localStorage.removeItem(this.storageKey)\n    localStorage.removeItem(this.rotationsKey)\n    console.log('✅ تم إعادة تعيين التايم لاين')\n  }\n}\n\nexport const timelineService = new TimelineService()\nexport default timelineService\n"], "file": "assets/timelineService-DsyKnWJK.js"}