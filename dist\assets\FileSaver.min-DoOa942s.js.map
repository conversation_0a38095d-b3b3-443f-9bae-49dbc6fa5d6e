{"version": 3, "file": "FileSaver.min-DoOa942s.js", "sources": ["../../node_modules/file-saver/dist/FileSaver.min.js"], "sourcesContent": ["(function(a,b){if(\"function\"==typeof define&&define.amd)define([],b);else if(\"undefined\"!=typeof exports)b();else{b(),a.FileSaver={exports:{}}.exports}})(this,function(){\"use strict\";function b(a,b){return\"undefined\"==typeof b?b={autoBom:!1}:\"object\"!=typeof b&&(console.warn(\"Deprecated: Expected third argument to be a object\"),b={autoBom:!b}),b.autoBom&&/^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type)?new Blob([\"\\uFEFF\",a],{type:a.type}):a}function c(a,b,c){var d=new XMLHttpRequest;d.open(\"GET\",a),d.responseType=\"blob\",d.onload=function(){g(d.response,b,c)},d.onerror=function(){console.error(\"could not download file\")},d.send()}function d(a){var b=new XMLHttpRequest;b.open(\"HEAD\",a,!1);try{b.send()}catch(a){}return 200<=b.status&&299>=b.status}function e(a){try{a.dispatchEvent(new MouseEvent(\"click\"))}catch(c){var b=document.createEvent(\"MouseEvents\");b.initMouseEvent(\"click\",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var f=\"object\"==typeof window&&window.window===window?window:\"object\"==typeof self&&self.self===self?self:\"object\"==typeof global&&global.global===global?global:void 0,a=f.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),g=f.saveAs||(\"object\"!=typeof window||window!==f?function(){}:\"download\"in HTMLAnchorElement.prototype&&!a?function(b,g,h){var i=f.URL||f.webkitURL,j=document.createElement(\"a\");g=g||b.name||\"download\",j.download=g,j.rel=\"noopener\",\"string\"==typeof b?(j.href=b,j.origin===location.origin?e(j):d(j.href)?c(b,g,h):e(j,j.target=\"_blank\")):(j.href=i.createObjectURL(b),setTimeout(function(){i.revokeObjectURL(j.href)},4E4),setTimeout(function(){e(j)},0))}:\"msSaveOrOpenBlob\"in navigator?function(f,g,h){if(g=g||f.name||\"download\",\"string\"!=typeof f)navigator.msSaveOrOpenBlob(b(f,h),g);else if(d(f))c(f,g,h);else{var i=document.createElement(\"a\");i.href=f,i.target=\"_blank\",setTimeout(function(){e(i)})}}:function(b,d,e,g){if(g=g||open(\"\",\"_blank\"),g&&(g.document.title=g.document.body.innerText=\"downloading...\"),\"string\"==typeof b)return c(b,d,e);var h=\"application/octet-stream\"===b.type,i=/constructor/i.test(f.HTMLElement)||f.safari,j=/CriOS\\/[\\d]+/.test(navigator.userAgent);if((j||h&&i||a)&&\"undefined\"!=typeof FileReader){var k=new FileReader;k.onloadend=function(){var a=k.result;a=j?a:a.replace(/^data:[^;]*;/,\"data:attachment/file;\"),g?g.location.href=a:location=a,g=null},k.readAsDataURL(b)}else{var l=f.URL||f.webkitURL,m=l.createObjectURL(b);g?g.location=m:location.href=m,g=null,setTimeout(function(){l.revokeObjectURL(m)},4E4)}});f.saveAs=g.saveAs=g,\"undefined\"!=typeof module&&(module.exports=g)});\n\n//# sourceMappingURL=FileSaver.min.js.map"], "names": ["a", "b", "this", "c", "d", "g", "e", "f", "h", "i", "j", "k", "l", "module"], "mappings": "gaAAC,SAASA,EAAEC,EAAE,CAA2FA,EAAA,CAA8C,GAAGC,EAAK,UAAU,CAAc,SAASD,EAAED,EAAEC,EAAE,CAAC,OAAmB,OAAOA,EAApB,IAAsBA,EAAE,CAAC,QAAQ,IAAc,OAAOA,GAAjB,WAAqB,QAAQ,KAAK,oDAAoD,EAAEA,EAAE,CAAC,QAAQ,CAACA,CAAAA,GAAIA,EAAE,SAAS,6EAA6E,KAAKD,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,SAASA,CAAC,EAAE,CAAC,KAAKA,EAAE,IAAA,CAAK,EAAEA,CAAA,CAAE,SAASG,EAAEH,EAAEC,EAAEE,EAAE,CAAC,IAAIC,EAAE,IAAI,eAAeA,EAAE,KAAK,MAAMJ,CAAC,EAAEI,EAAE,aAAa,OAAOA,EAAE,OAAO,UAAU,CAACC,EAAED,EAAE,SAASH,EAAEE,CAAC,CAAA,EAAGC,EAAE,QAAQ,UAAU,CAAC,QAAQ,MAAM,yBAAyB,CAAA,EAAGA,EAAE,KAAA,CAAK,CAAE,SAASA,EAAEJ,EAAE,CAAC,IAAIC,EAAE,IAAI,eAAeA,EAAE,KAAK,OAAOD,EAAE,EAAE,EAAE,GAAG,CAACC,EAAE,KAAA,CAAK,MAAU,CAAA,CAAE,MAAO,MAAKA,EAAE,QAAQ,KAAKA,EAAE,MAAA,CAAO,SAASK,EAAEN,EAAE,CAAC,GAAG,CAACA,EAAE,cAAc,IAAI,WAAW,OAAO,CAAC,CAAA,MAAU,CAAC,IAAIC,EAAE,SAAS,YAAY,aAAa,EAAEA,EAAE,eAAe,QAAQ,GAAG,GAAG,OAAO,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAED,EAAE,cAAcC,CAAC,CAAA,CAAC,CAAE,IAAIM,EAAY,OAAO,QAAjB,UAAyB,OAAO,SAAS,OAAO,OAAiB,OAAO,MAAjB,UAAuB,KAAK,OAAO,KAAK,KAAe,OAAO,YAAjB,UAAyB,WAAO,SAAS,WAAO,WAAO,OAAOP,EAAEO,EAAE,WAAW,YAAY,KAAK,UAAU,SAAS,GAAG,cAAc,KAAK,UAAU,SAAS,GAAG,CAAC,SAAS,KAAK,UAAU,SAAS,EAAEF,EAAEE,EAAE,SAAmB,OAAO,QAAjB,UAAyB,SAASA,EAAE,UAAU,CAAA,EAAG,aAAa,kBAAkB,WAAW,CAACP,EAAE,SAASC,EAAEI,EAAEG,EAAE,CAAC,IAAIC,EAAEF,EAAE,KAAKA,EAAE,UAAUG,EAAE,SAAS,cAAc,GAAG,EAAEL,EAAEA,GAAGJ,EAAE,MAAM,WAAWS,EAAE,SAASL,EAAEK,EAAE,IAAI,WAAqB,OAAOT,GAAjB,UAAoBS,EAAE,KAAKT,EAAES,EAAE,SAAS,SAAS,OAAOJ,EAAEI,CAAC,EAAEN,EAAEM,EAAE,IAAI,EAAEP,EAAEF,EAAEI,EAAEG,CAAC,EAAEF,EAAEI,EAAEA,EAAE,OAAO,QAAQ,IAAIA,EAAE,KAAKD,EAAE,gBAAgBR,CAAC,EAAE,WAAW,UAAU,CAACQ,EAAE,gBAAgBC,EAAE,IAAI,CAAA,EAAG,GAAG,EAAE,WAAW,UAAU,CAACJ,EAAEI,CAAC,CAAA,EAAG,CAAC,EAAA,EAAI,qBAAqB,UAAU,SAASH,EAAEF,EAAEG,EAAE,CAAC,GAAGH,EAAEA,GAAGE,EAAE,MAAM,WAAqB,OAAOA,GAAjB,SAAmB,UAAU,iBAAiBN,EAAEM,EAAEC,CAAC,EAAEH,CAAC,UAAUD,EAAEG,CAAC,EAAEJ,EAAEI,EAAEF,EAAEG,CAAC,MAAM,CAAC,IAAIC,EAAE,SAAS,cAAc,GAAG,EAAEA,EAAE,KAAKF,EAAEE,EAAE,OAAO,SAAS,WAAW,UAAU,CAACH,EAAEG,CAAC,CAAA,CAAE,CAAA,CAAC,EAAG,SAASR,EAAEG,EAAEE,EAAED,EAAE,CAAC,GAAGA,EAAEA,GAAG,KAAK,GAAG,QAAQ,EAAEA,IAAIA,EAAE,SAAS,MAAMA,EAAE,SAAS,KAAK,UAAU,kBAA4B,OAAOJ,GAAjB,SAAmB,OAAOE,EAAEF,EAAEG,EAAEE,CAAC,EAAE,IAAIE,EAA+BP,EAAE,OAA/B,2BAAoCQ,EAAE,eAAe,KAAKF,EAAE,WAAW,GAAGA,EAAE,OAAOG,EAAE,eAAe,KAAK,UAAU,SAAS,EAAE,IAAIA,GAAGF,GAAGC,GAAGT,IAAiB,OAAO,WAApB,IAA+B,CAAC,IAAIW,EAAE,IAAI,WAAWA,EAAE,UAAU,UAAU,CAAC,IAAIX,EAAEW,EAAE,OAAOX,EAAEU,EAAEV,EAAEA,EAAE,QAAQ,eAAe,uBAAuB,EAAEK,EAAEA,EAAE,SAAS,KAAKL,EAAE,SAASA,EAAEK,EAAE,IAAA,EAAMM,EAAE,cAAcV,CAAC,CAAA,KAAM,CAAC,IAAIW,EAAEL,EAAE,KAAKA,EAAE,UAAU,EAAEK,EAAE,gBAAgBX,CAAC,EAAEI,EAAEA,EAAE,SAAS,EAAE,SAAS,KAAK,EAAEA,EAAE,KAAK,WAAW,UAAU,CAACO,EAAE,gBAAgB,CAAC,CAAA,EAAG,GAAG,CAAA,CAAC,GAAIL,EAAE,OAAOF,EAAE,OAAOA,EAA+BQ,EAAA,QAAeR,CAAE,CAAC", "x_google_ignoreList": [0]}